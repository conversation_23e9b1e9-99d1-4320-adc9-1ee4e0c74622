-- 🌐 A2A Integration Schema - Database Foundation for Agent-to-Agent Protocol
-- Migration 008: A2A Integration with Email Analysis and Interface

-- ============================================================================
-- A2A CORE TABLES
-- ============================================================================

-- A2A Conversations - Persistent conversation contexts
CREATE TABLE a2a_conversations (
    id BIGSERIAL PRIMARY KEY,
    context_id VARCHAR(255) UNIQUE NOT NULL,
    customer_id BIGINT REFERENCES customers(id),
    agent_name VARCHAR(255) NOT NULL DEFAULT 'HVAC CRM Intelligence Agent',
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, completed, archived
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_conversations_context_id (context_id),
    INDEX idx_a2a_conversations_customer_id (customer_id),
    INDEX idx_a2a_conversations_status (status),
    INDEX idx_a2a_conversations_created_at (created_at)
);

-- A2A Tasks - Task lifecycle management
CREATE TABLE a2a_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(255) UNIQUE NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    skill_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'submitted', -- submitted, working, input-required, completed, failed, canceled
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    input_data JSONB NOT NULL DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_tasks_task_id (task_id),
    INDEX idx_a2a_tasks_conversation_id (conversation_id),
    INDEX idx_a2a_tasks_skill_name (skill_name),
    INDEX idx_a2a_tasks_status (status),
    INDEX idx_a2a_tasks_priority (priority),
    INDEX idx_a2a_tasks_created_at (created_at)
);

-- A2A Messages - Message history within conversations
CREATE TABLE a2a_messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    role VARCHAR(20) NOT NULL, -- user, agent
    content JSONB NOT NULL, -- message parts
    token_usage JSONB, -- input/output token counts
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_messages_message_id (message_id),
    INDEX idx_a2a_messages_conversation_id (conversation_id),
    INDEX idx_a2a_messages_task_id (task_id),
    INDEX idx_a2a_messages_role (role),
    INDEX idx_a2a_messages_created_at (created_at)
);

-- A2A Skill Executions - Tracking skill usage and performance
CREATE TABLE a2a_skill_executions (
    id BIGSERIAL PRIMARY KEY,
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    task_id BIGINT REFERENCES a2a_tasks(id),
    skill_name VARCHAR(255) NOT NULL,
    execution_type VARCHAR(50) NOT NULL, -- direct, mcp_bridge, langchain_bridge, fallback
    input_size INTEGER DEFAULT 0,
    output_size INTEGER DEFAULT 0,
    execution_time_ms INTEGER DEFAULT 0,
    success BOOLEAN DEFAULT FALSE,
    error_code VARCHAR(50),
    error_message TEXT,
    bridge_tool VARCHAR(255), -- MCP tool or LangChain chain used
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_skill_executions_execution_id (execution_id),
    INDEX idx_a2a_skill_executions_task_id (task_id),
    INDEX idx_a2a_skill_executions_skill_name (skill_name),
    INDEX idx_a2a_skill_executions_execution_type (execution_type),
    INDEX idx_a2a_skill_executions_success (success),
    INDEX idx_a2a_skill_executions_created_at (created_at)
);

-- ============================================================================
-- EMAIL-A2A INTEGRATION TABLES
-- ============================================================================

-- Email A2A Processing - Links emails to A2A tasks
CREATE TABLE email_a2a_processing (
    id BIGSERIAL PRIMARY KEY,
    email_id VARCHAR(255) NOT NULL, -- from email intelligence
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    processing_status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    intent_classification VARCHAR(255),
    confidence_score DECIMAL(5,4), -- 0.0000 to 1.0000
    extracted_entities JSONB DEFAULT '{}',
    automated_response BOOLEAN DEFAULT FALSE,
    response_sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_email_a2a_processing_email_id (email_id),
    INDEX idx_email_a2a_processing_conversation_id (conversation_id),
    INDEX idx_email_a2a_processing_task_id (task_id),
    INDEX idx_email_a2a_processing_status (processing_status),
    INDEX idx_email_a2a_processing_intent (intent_classification),
    INDEX idx_email_a2a_processing_created_at (created_at)
);

-- Transcription A2A Analysis - Links transcriptions to A2A insights
CREATE TABLE transcription_a2a_analysis (
    id BIGSERIAL PRIMARY KEY,
    transcription_id VARCHAR(255) NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    analysis_type VARCHAR(50) NOT NULL, -- sentiment, intent, entity_extraction, summary
    analysis_result JSONB NOT NULL,
    confidence_score DECIMAL(5,4),
    actionable_insights JSONB DEFAULT '{}',
    follow_up_actions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_transcription_a2a_analysis_transcription_id (transcription_id),
    INDEX idx_transcription_a2a_analysis_conversation_id (conversation_id),
    INDEX idx_transcription_a2a_analysis_task_id (task_id),
    INDEX idx_transcription_a2a_analysis_type (analysis_type),
    INDEX idx_transcription_a2a_analysis_created_at (created_at)
);

-- Customer A2A Interactions - Customer-specific A2A history
CREATE TABLE customer_a2a_interactions (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id),
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    interaction_type VARCHAR(50) NOT NULL, -- inquiry, diagnostic, quote, scheduling, support
    interaction_summary TEXT,
    satisfaction_score INTEGER, -- 1-5 rating
    resolution_status VARCHAR(50) DEFAULT 'open', -- open, in_progress, resolved, escalated
    business_value DECIMAL(10,2), -- estimated value generated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_customer_a2a_interactions_customer_id (customer_id),
    INDEX idx_customer_a2a_interactions_conversation_id (conversation_id),
    INDEX idx_customer_a2a_interactions_type (interaction_type),
    INDEX idx_customer_a2a_interactions_status (resolution_status),
    INDEX idx_customer_a2a_interactions_created_at (created_at)
);

-- ============================================================================
-- WORKFLOW AND AUTOMATION TABLES
-- ============================================================================

-- A2A Workflows - Automated workflows triggered by A2A
CREATE TABLE a2a_workflows (
    id BIGSERIAL PRIMARY KEY,
    workflow_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL, -- email, task_completion, schedule, webhook
    trigger_conditions JSONB NOT NULL DEFAULT '{}',
    workflow_steps JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, paused, disabled
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    last_executed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_workflows_workflow_id (workflow_id),
    INDEX idx_a2a_workflows_trigger_type (trigger_type),
    INDEX idx_a2a_workflows_status (status),
    INDEX idx_a2a_workflows_created_at (created_at)
);

-- A2A Workflow Executions - Track workflow runs
CREATE TABLE a2a_workflow_executions (
    id BIGSERIAL PRIMARY KEY,
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    workflow_id BIGINT REFERENCES a2a_workflows(id),
    trigger_data JSONB DEFAULT '{}',
    execution_status VARCHAR(50) NOT NULL DEFAULT 'running', -- running, completed, failed, canceled
    steps_completed INTEGER DEFAULT 0,
    total_steps INTEGER DEFAULT 0,
    execution_time_ms INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_workflow_executions_execution_id (execution_id),
    INDEX idx_a2a_workflow_executions_workflow_id (workflow_id),
    INDEX idx_a2a_workflow_executions_status (execution_status),
    INDEX idx_a2a_workflow_executions_started_at (started_at)
);

-- ============================================================================
-- METRICS AND ANALYTICS TABLES
-- ============================================================================

-- A2A Metrics - Performance and usage metrics
CREATE TABLE a2a_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL, -- request_count, skill_usage, response_time, error_rate
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    dimensions JSONB DEFAULT '{}', -- additional metric dimensions
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    aggregation_period VARCHAR(20) DEFAULT 'hour', -- minute, hour, day, week, month
    
    -- Indexes
    INDEX idx_a2a_metrics_type (metric_type),
    INDEX idx_a2a_metrics_name (metric_name),
    INDEX idx_a2a_metrics_timestamp (timestamp),
    INDEX idx_a2a_metrics_period (aggregation_period)
);

-- ============================================================================
-- VIEWS FOR ANALYTICS
-- ============================================================================

-- A2A Performance Summary View
CREATE VIEW a2a_performance_summary AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    skill_name,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE success = true) as successful_executions,
    ROUND(AVG(execution_time_ms), 2) as avg_execution_time_ms,
    ROUND((COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)), 2) as success_rate
FROM a2a_skill_executions
GROUP BY DATE_TRUNC('day', created_at), skill_name
ORDER BY date DESC, skill_name;

-- Customer A2A Engagement View
CREATE VIEW customer_a2a_engagement AS
SELECT 
    c.id as customer_id,
    c.name as customer_name,
    COUNT(DISTINCT cai.conversation_id) as total_conversations,
    COUNT(cai.id) as total_interactions,
    AVG(cai.satisfaction_score) as avg_satisfaction,
    SUM(cai.business_value) as total_business_value,
    MAX(cai.created_at) as last_interaction_at
FROM customers c
LEFT JOIN customer_a2a_interactions cai ON c.id = cai.customer_id
GROUP BY c.id, c.name
ORDER BY total_interactions DESC;

-- Email to A2A Conversion View
CREATE VIEW email_a2a_conversion AS
SELECT
    DATE_TRUNC('day', created_at) as date,
    intent_classification,
    COUNT(*) as total_emails,
    COUNT(*) FILTER (WHERE processing_status = 'completed') as processed_emails,
    COUNT(*) FILTER (WHERE automated_response = true) as automated_responses,
    ROUND(AVG(confidence_score), 4) as avg_confidence
FROM email_a2a_processing
GROUP BY DATE_TRUNC('day', created_at), intent_classification
ORDER BY date DESC, intent_classification;

-- ============================================================================
-- DATA FLOW TRACKING TABLES
-- ============================================================================

-- Data Flow Records - Complete data flow tracking
CREATE TABLE data_flow_records (
    id BIGSERIAL PRIMARY KEY,
    flow_id VARCHAR(255) UNIQUE NOT NULL,
    flow_type VARCHAR(50) NOT NULL, -- email, api, webhook, manual
    status VARCHAR(50) NOT NULL DEFAULT 'started', -- started, processing, completed, failed
    conversation_id VARCHAR(255),
    customer_id VARCHAR(255),
    input_data JSONB NOT NULL DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes
    INDEX idx_data_flow_records_flow_id (flow_id),
    INDEX idx_data_flow_records_type (flow_type),
    INDEX idx_data_flow_records_status (status),
    INDEX idx_data_flow_records_conversation_id (conversation_id),
    INDEX idx_data_flow_records_customer_id (customer_id),
    INDEX idx_data_flow_records_created_at (created_at)
);

-- Real-time Sync Events - WebSocket event tracking
CREATE TABLE realtime_sync_events (
    id BIGSERIAL PRIMARY KEY,
    event_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- conversation, task, message, flow, metric
    event_action VARCHAR(50) NOT NULL, -- create, update, delete, status_change
    entity_id VARCHAR(255) NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',
    source VARCHAR(50) NOT NULL, -- email, a2a, database, interface
    priority INTEGER DEFAULT 2, -- 1=high, 2=medium, 3=low
    subscribers JSONB DEFAULT '[]', -- array of connection IDs
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',

    -- Indexes
    INDEX idx_realtime_sync_events_event_id (event_id),
    INDEX idx_realtime_sync_events_type (event_type),
    INDEX idx_realtime_sync_events_action (event_action),
    INDEX idx_realtime_sync_events_entity_id (entity_id),
    INDEX idx_realtime_sync_events_source (source),
    INDEX idx_realtime_sync_events_priority (priority),
    INDEX idx_realtime_sync_events_created_at (created_at)
);

-- Enhanced Email Processing - Advanced email processing tracking
CREATE TABLE enhanced_email_processing (
    id BIGSERIAL PRIMARY KEY,
    processing_id VARCHAR(255) UNIQUE NOT NULL,
    email_id VARCHAR(255) NOT NULL,
    conversation_id VARCHAR(255),
    processing_status VARCHAR(50) NOT NULL DEFAULT 'started', -- started, processing, completed, failed
    intent_result JSONB DEFAULT '{}',
    entity_result JSONB DEFAULT '{}',
    sentiment_result JSONB DEFAULT '{}',
    a2a_results JSONB DEFAULT '{}',
    automated_response JSONB DEFAULT '{}',
    processing_time_ms INTEGER DEFAULT 0,
    confidence_score DECIMAL(5,4),
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent, critical
    errors JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',

    -- Indexes
    INDEX idx_enhanced_email_processing_processing_id (processing_id),
    INDEX idx_enhanced_email_processing_email_id (email_id),
    INDEX idx_enhanced_email_processing_conversation_id (conversation_id),
    INDEX idx_enhanced_email_processing_status (processing_status),
    INDEX idx_enhanced_email_processing_priority (priority),
    INDEX idx_enhanced_email_processing_confidence (confidence_score),
    INDEX idx_enhanced_email_processing_created_at (created_at)
);

-- ============================================================================
-- ENHANCED ANALYTICS VIEWS
-- ============================================================================

-- Data Flow Performance View
CREATE VIEW data_flow_performance AS
SELECT
    DATE_TRUNC('hour', created_at) as hour,
    flow_type,
    COUNT(*) as total_flows,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_flows,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_flows,
    ROUND((COUNT(*) FILTER (WHERE status = 'completed') * 100.0 / COUNT(*)), 2) as success_rate,
    ROUND(AVG(EXTRACT(EPOCH FROM (updated_at - created_at))), 2) as avg_duration_seconds
FROM data_flow_records
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', created_at), flow_type
ORDER BY hour DESC, flow_type;

-- Real-time Activity View
CREATE VIEW realtime_activity AS
SELECT
    DATE_TRUNC('minute', created_at) as minute,
    event_type,
    event_action,
    source,
    COUNT(*) as event_count,
    COUNT(DISTINCT entity_id) as unique_entities,
    AVG(priority) as avg_priority
FROM realtime_sync_events
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY DATE_TRUNC('minute', created_at), event_type, event_action, source
ORDER BY minute DESC;

-- Enhanced Email Analytics View
CREATE VIEW enhanced_email_analytics AS
SELECT
    DATE_TRUNC('day', created_at) as date,
    priority,
    processing_status,
    COUNT(*) as total_emails,
    ROUND(AVG(confidence_score), 4) as avg_confidence,
    ROUND(AVG(processing_time_ms), 2) as avg_processing_time_ms,
    COUNT(*) FILTER (WHERE automated_response->>'response_sent' = 'true') as automated_responses,
    COUNT(*) FILTER (WHERE jsonb_array_length(errors) > 0) as emails_with_errors
FROM enhanced_email_processing
GROUP BY DATE_TRUNC('day', created_at), priority, processing_status
ORDER BY date DESC, priority, processing_status;

-- Customer A2A Interaction Summary View
CREATE VIEW customer_a2a_summary AS
SELECT
    c.id as customer_id,
    c.name as customer_name,
    c.email as customer_email,
    COUNT(DISTINCT ac.id) as total_conversations,
    COUNT(DISTINCT at.id) as total_tasks,
    COUNT(DISTINCT am.id) as total_messages,
    COUNT(DISTINCT cai.id) as total_interactions,
    AVG(cai.satisfaction_score) as avg_satisfaction,
    SUM(cai.business_value) as total_business_value,
    MAX(ac.updated_at) as last_a2a_activity,
    COUNT(*) FILTER (WHERE ac.status = 'active') as active_conversations
FROM customers c
LEFT JOIN a2a_conversations ac ON c.id = ac.customer_id
LEFT JOIN a2a_tasks at ON ac.id = at.conversation_id
LEFT JOIN a2a_messages am ON ac.id = am.conversation_id
LEFT JOIN customer_a2a_interactions cai ON c.id = cai.customer_id
GROUP BY c.id, c.name, c.email
HAVING COUNT(DISTINCT ac.id) > 0
ORDER BY total_interactions DESC, last_a2a_activity DESC;
