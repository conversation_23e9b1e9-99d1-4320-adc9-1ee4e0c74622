-- 🏭 Equipment Catalog Database Migration
-- Automated HVAC Equipment Database with Crawl4AI Integration
-- GoBackend-Kratos HVAC CRM System

-- Create manufacturers table
CREATE TABLE IF NOT EXISTS manufacturers (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(100) NOT NULL UNIQUE,
    display_name <PERSON><PERSON><PERSON><PERSON>(100),
    website VARCHAR(255),
    api_endpoint VARCHAR(255),
    api_key VARCHAR(255),
    logo_url VARCHAR(500),
    
    -- Crawling Configuration
    crawl_config JSONB,
    last_crawled TIMESTAMP,
    crawl_frequency INTEGER DEFAULT 24, -- hours
    
    -- Contact Information
    support_phone VARCHAR(50),
    support_email VARCHAR(100),
    technical_support VARCHAR(100),
    
    -- Business Information
    is_active BOOLEAN DEFAULT true,
    country VARCHAR(50),
    region VARCHAR(50),
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create equipment_catalog table
CREATE TABLE IF NOT EXISTS equipment_catalog (
    id BIGSERIAL PRIMARY KEY,
    manufacturer_id BIGINT NOT NULL REFERENCES manufacturers(id) ON DELETE CASCADE,
    model_number VARCHAR(100) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_family VARCHAR(100),
    category VARCHAR(100) NOT NULL, -- air_conditioner, heat_pump, furnace, etc.
    sub_category VARCHAR(100),
    
    -- Technical Specifications (JSONB for flexibility)
    specifications JSONB,
    performance_data JSONB,
    dimensions_data JSONB,
    electrical_data JSONB,
    
    -- Capacity & Efficiency (indexed for fast queries)
    cooling_capacity_btu BIGINT,
    heating_capacity_btu BIGINT,
    seer DECIMAL(4,2),
    eer DECIMAL(4,2),
    hspf DECIMAL(4,2),
    cop DECIMAL(4,2),
    
    -- Physical Properties
    weight DECIMAL(8,2), -- in pounds
    length DECIMAL(8,2), -- in inches
    width DECIMAL(8,2),  -- in inches
    height DECIMAL(8,2), -- in inches
    
    -- Pricing & Availability
    msrp DECIMAL(10,2),
    dealer_price DECIMAL(10,2),
    is_available BOOLEAN DEFAULT true,
    discontinued_date TIMESTAMP,
    
    -- Certifications & Standards
    ahri_number VARCHAR(50),
    energy_star BOOLEAN DEFAULT false,
    certifications TEXT[],
    
    -- Images & Documentation
    image_urls TEXT[],
    document_urls TEXT[],
    technical_sheet_url VARCHAR(500),
    installation_guide_url VARCHAR(500),
    
    -- Data Source & Quality
    data_source VARCHAR(100), -- crawl4ai, api, manual
    source_url VARCHAR(500),
    data_quality_score DECIMAL(3,2) DEFAULT 0.0, -- 0.0 to 1.0
    last_verified TIMESTAMP,
    
    -- AI Analysis
    ai_analysis_data JSONB,
    recommendation_score DECIMAL(3,2) DEFAULT 0.0,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create catalog_images table for MinIO integration
CREATE TABLE IF NOT EXISTS catalog_images (
    id BIGSERIAL PRIMARY KEY,
    catalog_id BIGINT NOT NULL REFERENCES equipment_catalog(id) ON DELETE CASCADE,
    image_type VARCHAR(50), -- product, technical, installation, etc.
    file_name VARCHAR(255) NOT NULL,
    minio_path VARCHAR(500) NOT NULL,
    minio_bucket VARCHAR(100) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    width INTEGER,
    height INTEGER,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create equipment_compatibility table
CREATE TABLE IF NOT EXISTS equipment_compatibility (
    id BIGSERIAL PRIMARY KEY,
    catalog_id BIGINT NOT NULL REFERENCES equipment_catalog(id) ON DELETE CASCADE,
    equipment_id BIGINT NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    compatibility_type VARCHAR(50), -- replacement, upgrade, accessory
    compatibility_score DECIMAL(3,2) DEFAULT 0.0, -- 0.0 to 1.0
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create crawl_jobs table for automated data collection
CREATE TABLE IF NOT EXISTS crawl_jobs (
    id BIGSERIAL PRIMARY KEY,
    manufacturer_id BIGINT NOT NULL REFERENCES manufacturers(id) ON DELETE CASCADE,
    job_type VARCHAR(50) NOT NULL, -- full_catalog, incremental, images, specs
    status VARCHAR(50) NOT NULL,   -- pending, running, completed, failed
    priority INTEGER DEFAULT 5,    -- 1-10, higher is more priority
    
    -- Job Configuration
    crawl_config JSONB,
    target_urls TEXT[],
    
    -- Progress Tracking
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    successful_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    
    -- Results
    result_data JSONB,
    error_log TEXT,
    
    -- Timing
    scheduled_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration BIGINT, -- seconds
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_manufacturers_name ON manufacturers(name);
CREATE INDEX IF NOT EXISTS idx_manufacturers_active ON manufacturers(is_active);
CREATE INDEX IF NOT EXISTS idx_manufacturers_last_crawled ON manufacturers(last_crawled);

CREATE INDEX IF NOT EXISTS idx_equipment_catalog_manufacturer ON equipment_catalog(manufacturer_id);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_model ON equipment_catalog(model_number);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_category ON equipment_catalog(category);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_available ON equipment_catalog(is_available);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_cooling_capacity ON equipment_catalog(cooling_capacity_btu);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_heating_capacity ON equipment_catalog(heating_capacity_btu);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_seer ON equipment_catalog(seer);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_ahri ON equipment_catalog(ahri_number);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_energy_star ON equipment_catalog(energy_star);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_data_quality ON equipment_catalog(data_quality_score);

-- GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_specifications ON equipment_catalog USING GIN(specifications);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_performance ON equipment_catalog USING GIN(performance_data);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_ai_analysis ON equipment_catalog USING GIN(ai_analysis_data);

-- Array indexes
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_certifications ON equipment_catalog USING GIN(certifications);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_image_urls ON equipment_catalog USING GIN(image_urls);

CREATE INDEX IF NOT EXISTS idx_catalog_images_catalog ON catalog_images(catalog_id);
CREATE INDEX IF NOT EXISTS idx_catalog_images_type ON catalog_images(image_type);
CREATE INDEX IF NOT EXISTS idx_catalog_images_primary ON catalog_images(is_primary);

CREATE INDEX IF NOT EXISTS idx_equipment_compatibility_catalog ON equipment_compatibility(catalog_id);
CREATE INDEX IF NOT EXISTS idx_equipment_compatibility_equipment ON equipment_compatibility(equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_compatibility_type ON equipment_compatibility(compatibility_type);
CREATE INDEX IF NOT EXISTS idx_equipment_compatibility_score ON equipment_compatibility(compatibility_score);

CREATE INDEX IF NOT EXISTS idx_crawl_jobs_manufacturer ON crawl_jobs(manufacturer_id);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_status ON crawl_jobs(status);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_type ON crawl_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_priority ON crawl_jobs(priority);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_scheduled ON crawl_jobs(scheduled_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_manufacturer_category ON equipment_catalog(manufacturer_id, category);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_category_available ON equipment_catalog(category, is_available);
CREATE INDEX IF NOT EXISTS idx_equipment_catalog_capacity_range ON equipment_catalog(cooling_capacity_btu, heating_capacity_btu);

-- Insert default manufacturers
INSERT INTO manufacturers (name, display_name, website, is_active, country) VALUES
('daikin', 'Daikin', 'https://www.daikincomfort.com', true, 'US'),
('lg', 'LG Electronics', 'https://www.lg.com', true, 'KR'),
('carrier', 'Carrier', 'https://www.carrier.com', true, 'US'),
('trane', 'Trane', 'https://www.trane.com', true, 'US'),
('lennox', 'Lennox', 'https://www.lennox.com', true, 'US'),
('rheem', 'Rheem', 'https://www.rheem.com', true, 'US'),
('goodman', 'Goodman', 'https://www.goodmanmfg.com', true, 'US'),
('york', 'York', 'https://www.york.com', true, 'US'),
('mitsubishi', 'Mitsubishi Electric', 'https://www.mitsubishicomfort.com', true, 'JP'),
('fujitsu', 'Fujitsu General', 'https://www.fujitsugeneral.com', true, 'JP')
ON CONFLICT (name) DO NOTHING;

-- Update manufacturers with default crawl configurations
UPDATE manufacturers SET crawl_config = jsonb_build_object(
    'base_url', website,
    'catalog_urls', ARRAY[website || '/products'],
    'wait_time', 3,
    'use_api', false
) WHERE crawl_config IS NULL;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_manufacturers_updated_at BEFORE UPDATE ON manufacturers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_equipment_catalog_updated_at BEFORE UPDATE ON equipment_catalog
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crawl_jobs_updated_at BEFORE UPDATE ON crawl_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE manufacturers IS 'HVAC equipment manufacturers with crawling configuration';
COMMENT ON TABLE equipment_catalog IS 'Comprehensive HVAC equipment catalog with AI-extracted specifications';
COMMENT ON TABLE catalog_images IS 'Equipment images stored in MinIO with metadata';
COMMENT ON TABLE equipment_compatibility IS 'Equipment compatibility matrix for recommendations';
COMMENT ON TABLE crawl_jobs IS 'Automated data collection job tracking';

COMMENT ON COLUMN equipment_catalog.specifications IS 'Technical specifications in JSONB format';
COMMENT ON COLUMN equipment_catalog.data_quality_score IS 'AI-assessed data quality score (0.0-1.0)';
COMMENT ON COLUMN equipment_catalog.recommendation_score IS 'AI recommendation score for equipment selection';
COMMENT ON COLUMN crawl_jobs.crawl_config IS 'Job-specific crawling configuration';
COMMENT ON COLUMN crawl_jobs.result_data IS 'Crawling results and extracted data';
