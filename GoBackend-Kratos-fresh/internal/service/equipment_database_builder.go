package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/crawl4ai"
	"gobackend-hvac-kratos/internal/entity"
)

// 🏗️ Equipment Database Builder Service
// Automated HVAC Equipment Catalog Management with Crawl4AI Integration

// EquipmentDatabaseBuilderService manages automated equipment data collection
type EquipmentDatabaseBuilderService struct {
	log                *log.Helper
	crawl4aiClient     *crawl4ai.Crawl4AIClient
	gemma3Service      *ai.Gemma3Service
	equipmentCatalogUc *biz.EquipmentCatalogUsecase
	fileStorageUc      *biz.FileStorageUsecase
	config             *DatabaseBuilderConfig
}

// DatabaseBuilderConfig represents configuration for the database builder
type DatabaseBuilderConfig struct {
	CrawlInterval      time.Duration `yaml:"crawl_interval"`       // 24h
	MaxConcurrentJobs  int           `yaml:"max_concurrent_jobs"`  // 5
	ImageDownloadLimit int           `yaml:"image_download_limit"` // 10 per product
	DataQualityThreshold float64     `yaml:"data_quality_threshold"` // 0.7
	EnableAIAnalysis   bool          `yaml:"enable_ai_analysis"`   // true
	EnableImageDownload bool         `yaml:"enable_image_download"` // true
	RetryAttempts      int           `yaml:"retry_attempts"`       // 3
	RateLimitDelay     time.Duration `yaml:"rate_limit_delay"`     // 2s
}

// ManufacturerCrawlConfig represents crawling configuration for a manufacturer
type ManufacturerCrawlConfig struct {
	BaseURL           string            `json:"base_url"`
	CatalogURLs       []string          `json:"catalog_urls"`
	ProductURLPattern string            `json:"product_url_pattern"`
	Selectors         map[string]string `json:"selectors"`
	ExcludePatterns   []string          `json:"exclude_patterns"`
	CustomHeaders     map[string]string `json:"custom_headers"`
	WaitTime          int               `json:"wait_time"`
	UseAPI            bool              `json:"use_api"`
	APIConfig         *APIConfig        `json:"api_config,omitempty"`
}

// APIConfig represents API configuration for manufacturers with official APIs
type APIConfig struct {
	Endpoint    string            `json:"endpoint"`
	APIKey      string            `json:"api_key"`
	AuthMethod  string            `json:"auth_method"` // bearer, basic, api_key
	Headers     map[string]string `json:"headers"`
	RateLimit   int               `json:"rate_limit"` // requests per minute
}

// CrawlJobResult represents the result of a crawl job
type CrawlJobResult struct {
	JobID           int64                    `json:"job_id"`
	ManufacturerID  int64                    `json:"manufacturer_id"`
	Status          string                   `json:"status"`
	ProcessedItems  int                      `json:"processed_items"`
	SuccessfulItems int                      `json:"successful_items"`
	FailedItems     int                      `json:"failed_items"`
	ExtractedData   []*ExtractedEquipmentData `json:"extracted_data"`
	Errors          []string                 `json:"errors"`
	Duration        time.Duration            `json:"duration"`
	DataQuality     float64                  `json:"data_quality"`
}

// ExtractedEquipmentData represents equipment data extracted from crawling
type ExtractedEquipmentData struct {
	ModelNumber        string                 `json:"model_number"`
	ProductName        string                 `json:"product_name"`
	ProductFamily      string                 `json:"product_family"`
	Category           string                 `json:"category"`
	Specifications     map[string]interface{} `json:"specifications"`
	PerformanceData    map[string]interface{} `json:"performance_data"`
	DimensionsData     map[string]interface{} `json:"dimensions_data"`
	ElectricalData     map[string]interface{} `json:"electrical_data"`
	CoolingCapacityBTU *int64                 `json:"cooling_capacity_btu"`
	HeatingCapacityBTU *int64                 `json:"heating_capacity_btu"`
	SEER               *float64               `json:"seer"`
	EER                *float64               `json:"eer"`
	HSPF               *float64               `json:"hspf"`
	COP                *float64               `json:"cop"`
	Weight             *float64               `json:"weight"`
	Dimensions         map[string]float64     `json:"dimensions"`
	MSRP               *float64               `json:"msrp"`
	AHRINumber         string                 `json:"ahri_number"`
	EnergyStar         bool                   `json:"energy_star"`
	Certifications     []string               `json:"certifications"`
	ImageURLs          []string               `json:"image_urls"`
	DocumentURLs       []string               `json:"document_urls"`
	SourceURL          string                 `json:"source_url"`
	DataQualityScore   float64                `json:"data_quality_score"`
}

// NewEquipmentDatabaseBuilderService creates a new equipment database builder service
func NewEquipmentDatabaseBuilderService(
	crawl4aiClient *crawl4ai.Crawl4AIClient,
	gemma3Service *ai.Gemma3Service,
	equipmentCatalogUc *biz.EquipmentCatalogUsecase,
	fileStorageUc *biz.FileStorageUsecase,
	config *DatabaseBuilderConfig,
	logger log.Logger,
) *EquipmentDatabaseBuilderService {
	return &EquipmentDatabaseBuilderService{
		log:                log.NewHelper(logger),
		crawl4aiClient:     crawl4aiClient,
		gemma3Service:      gemma3Service,
		equipmentCatalogUc: equipmentCatalogUc,
		fileStorageUc:      fileStorageUc,
		config:             config,
	}
}

// StartCrawlJob starts a new crawl job for a manufacturer
func (s *EquipmentDatabaseBuilderService) StartCrawlJob(ctx context.Context, manufacturerID int64, jobType string) (*entity.CrawlJob, error) {
	s.log.WithContext(ctx).Infof("Starting crawl job for manufacturer %d, type: %s", manufacturerID, jobType)

	// Get manufacturer information
	manufacturer, err := s.equipmentCatalogUc.GetManufacturer(ctx, manufacturerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get manufacturer: %w", err)
	}

	// Parse crawl configuration
	var crawlConfig ManufacturerCrawlConfig
	if manufacturer.CrawlConfig != nil {
		if err := json.Unmarshal(manufacturer.CrawlConfig, &crawlConfig); err != nil {
			return nil, fmt.Errorf("failed to parse crawl config: %w", err)
		}
	} else {
		// Use default configuration
		crawlConfig = s.getDefaultCrawlConfig(manufacturer.Name)
	}

	// Create crawl job
	job := &entity.CrawlJob{
		ManufacturerID: manufacturerID,
		JobType:        jobType,
		Status:         "pending",
		Priority:       5,
		ScheduledAt:    timePtr(time.Now()),
		TargetURLs:     crawlConfig.CatalogURLs,
	}

	// Set crawl configuration
	configData, err := json.Marshal(crawlConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal crawl config: %w", err)
	}
	job.CrawlConfig = configData

	// Save job to database
	createdJob, err := s.equipmentCatalogUc.CreateCrawlJob(ctx, job)
	if err != nil {
		return nil, fmt.Errorf("failed to create crawl job: %w", err)
	}

	// Start job execution asynchronously
	go s.executeCrawlJob(context.Background(), createdJob, crawlConfig)

	return createdJob, nil
}

// executeCrawlJob executes a crawl job
func (s *EquipmentDatabaseBuilderService) executeCrawlJob(ctx context.Context, job *entity.CrawlJob, config ManufacturerCrawlConfig) {
	s.log.WithContext(ctx).Infof("Executing crawl job %d", job.ID)

	// Update job status
	job.Status = "running"
	job.StartedAt = timePtr(time.Now())
	s.equipmentCatalogUc.UpdateCrawlJob(ctx, job)

	var result CrawlJobResult
	result.JobID = job.ID
	result.ManufacturerID = job.ManufacturerID
	result.Status = "running"

	startTime := time.Now()

	// Execute crawling based on configuration
	if config.UseAPI && config.APIConfig != nil {
		// Use official API
		s.log.WithContext(ctx).Info("Using official API for data collection")
		result = s.crawlUsingAPI(ctx, config)
	} else {
		// Use web scraping
		s.log.WithContext(ctx).Info("Using web scraping for data collection")
		result = s.crawlUsingWebScraping(ctx, config)
	}

	result.Duration = time.Since(startTime)

	// Process extracted data
	if len(result.ExtractedData) > 0 {
		s.processExtractedData(ctx, job.ManufacturerID, result.ExtractedData)
	}

	// Update job with results
	job.Status = result.Status
	job.CompletedAt = timePtr(time.Now())
	job.ProcessedItems = result.ProcessedItems
	job.SuccessfulItems = result.SuccessfulItems
	job.FailedItems = result.FailedItems
	job.Duration = int64Ptr(int64(result.Duration.Seconds()))

	// Store result data
	resultData, _ := json.Marshal(result)
	job.ResultData = resultData

	if len(result.Errors) > 0 {
		job.ErrorLog = strings.Join(result.Errors, "\n")
	}

	s.equipmentCatalogUc.UpdateCrawlJob(ctx, job)

	s.log.WithContext(ctx).Infof("Completed crawl job %d: %d processed, %d successful, %d failed",
		job.ID, result.ProcessedItems, result.SuccessfulItems, result.FailedItems)
}

// crawlUsingWebScraping performs web scraping using Crawl4AI
func (s *EquipmentDatabaseBuilderService) crawlUsingWebScraping(ctx context.Context, config ManufacturerCrawlConfig) CrawlJobResult {
	var result CrawlJobResult
	result.Status = "completed"

	for _, catalogURL := range config.CatalogURLs {
		s.log.WithContext(ctx).Infof("Crawling catalog: %s", catalogURL)

		// Crawl manufacturer catalog
		responses, err := s.crawl4aiClient.CrawlManufacturerCatalog(ctx, catalogURL, 50)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Failed to crawl %s: %v", catalogURL, err))
			result.FailedItems++
			continue
		}

		// Process each response
		for _, response := range responses {
			result.ProcessedItems++

			if response.Data == nil {
				result.FailedItems++
				continue
			}

			// Extract equipment data using AI
			extractedData, err := s.extractEquipmentDataFromCrawlResponse(ctx, response)
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Failed to extract data from %s: %v", response.Data.URL, err))
				result.FailedItems++
				continue
			}

			if extractedData != nil {
				result.ExtractedData = append(result.ExtractedData, extractedData)
				result.SuccessfulItems++
			}

			// Rate limiting
			time.Sleep(s.config.RateLimitDelay)
		}
	}

	// Calculate data quality
	if result.ProcessedItems > 0 {
		result.DataQuality = float64(result.SuccessfulItems) / float64(result.ProcessedItems)
	}

	return result
}

// crawlUsingAPI performs data collection using official APIs
func (s *EquipmentDatabaseBuilderService) crawlUsingAPI(ctx context.Context, config ManufacturerCrawlConfig) CrawlJobResult {
	var result CrawlJobResult
	result.Status = "completed"

	// Implementation for official APIs (Daikin, etc.)
	// This would be manufacturer-specific
	s.log.WithContext(ctx).Info("API-based crawling not yet implemented")

	return result
}

// extractEquipmentDataFromCrawlResponse extracts structured equipment data from crawl response
func (s *EquipmentDatabaseBuilderService) extractEquipmentDataFromCrawlResponse(ctx context.Context, response *crawl4ai.CrawlResponse) (*ExtractedEquipmentData, error) {
	if response.Data == nil {
		return nil, fmt.Errorf("no data in response")
	}

	// Use AI to extract structured data
	if s.config.EnableAIAnalysis && s.gemma3Service != nil {
		return s.extractUsingAI(ctx, response.Data)
	}

	// Fallback to basic extraction
	return s.extractUsingBasicParsing(ctx, response.Data)
}

// extractUsingAI uses Gemma3 AI to extract equipment data
func (s *EquipmentDatabaseBuilderService) extractUsingAI(ctx context.Context, data *crawl4ai.CrawlData) (*ExtractedEquipmentData, error) {
	// Prepare AI analysis request
	analysisReq := &ai.HVACEquipmentAnalysisRequest{
		Content:     data.Markdown,
		URL:         data.URL,
		Title:       data.Title,
		Images:      s.convertImagesToAIFormat(data.Images),
		AnalysisType: "equipment_extraction",
	}

	// Perform AI analysis
	response, err := s.gemma3Service.AnalyzeHVACEquipment(ctx, analysisReq)
	if err != nil {
		return nil, fmt.Errorf("AI analysis failed: %w", err)
	}

	// Convert AI response to ExtractedEquipmentData
	return s.convertAIResponseToEquipmentData(response, data.URL), nil
}

// extractUsingBasicParsing performs basic parsing without AI
func (s *EquipmentDatabaseBuilderService) extractUsingBasicParsing(ctx context.Context, data *crawl4ai.CrawlData) (*ExtractedEquipmentData, error) {
	extracted := &ExtractedEquipmentData{
		SourceURL:        data.URL,
		DataQualityScore: 0.3, // Lower quality for basic parsing
	}

	// Basic extraction from title and metadata
	if data.Title != "" {
		extracted.ProductName = data.Title
	}

	// Extract from extracted data if available
	if data.ExtractedData != nil {
		s.parseExtractedData(data.ExtractedData, extracted)
	}

	// Extract images
	for _, img := range data.Images {
		extracted.ImageURLs = append(extracted.ImageURLs, img.URL)
	}

	return extracted, nil
}

// Helper functions
func (s *EquipmentDatabaseBuilderService) getDefaultCrawlConfig(manufacturerName string) ManufacturerCrawlConfig {
	// Return default configuration based on manufacturer
	switch strings.ToLower(manufacturerName) {
	case "daikin":
		return ManufacturerCrawlConfig{
			BaseURL:     "https://www.daikincomfort.com",
			CatalogURLs: []string{"https://www.daikincomfort.com/products"},
			WaitTime:    3,
		}
	case "lg":
		return ManufacturerCrawlConfig{
			BaseURL:     "https://www.lg.com",
			CatalogURLs: []string{"https://www.lg.com/us/business/hvac"},
			WaitTime:    3,
		}
	default:
		return ManufacturerCrawlConfig{
			WaitTime: 3,
		}
	}
}

func (s *EquipmentDatabaseBuilderService) convertImagesToAIFormat(images []crawl4ai.Image) []*ai.ImageData {
	var aiImages []*ai.ImageData
	for _, img := range images {
		aiImages = append(aiImages, &ai.ImageData{
			URL:    img.URL,
			Alt:    img.Alt,
			Title:  img.Title,
			Width:  img.Width,
			Height: img.Height,
		})
	}
	return aiImages
}

func (s *EquipmentDatabaseBuilderService) convertAIResponseToEquipmentData(response *ai.HVACEquipmentAnalysisResponse, sourceURL string) *ExtractedEquipmentData {
	extracted := &ExtractedEquipmentData{
		SourceURL:        sourceURL,
		DataQualityScore: 0.8, // Higher quality for AI extraction
	}

	// Map AI response fields to extracted data
	if response.EquipmentSpecs != nil {
		extracted.ModelNumber = response.EquipmentSpecs.ModelNumber
		extracted.ProductName = response.EquipmentSpecs.ProductName
		extracted.Category = response.EquipmentSpecs.Category
		
		if response.EquipmentSpecs.CoolingCapacity != "" {
			if capacity, err := strconv.ParseInt(response.EquipmentSpecs.CoolingCapacity, 10, 64); err == nil {
				extracted.CoolingCapacityBTU = &capacity
			}
		}
		
		if response.EquipmentSpecs.SEER != "" {
			if seer, err := strconv.ParseFloat(response.EquipmentSpecs.SEER, 64); err == nil {
				extracted.SEER = &seer
			}
		}
	}

	return extracted
}

func (s *EquipmentDatabaseBuilderService) parseExtractedData(data map[string]interface{}, extracted *ExtractedEquipmentData) {
	// Parse extracted data from Crawl4AI
	for key, value := range data {
		switch key {
		case "model_number", "model":
			if str, ok := value.(string); ok {
				extracted.ModelNumber = str
			}
		case "product_name", "name":
			if str, ok := value.(string); ok {
				extracted.ProductName = str
			}
		case "seer":
			if str, ok := value.(string); ok {
				if seer, err := strconv.ParseFloat(str, 64); err == nil {
					extracted.SEER = &seer
				}
			}
		}
	}
}

func (s *EquipmentDatabaseBuilderService) processExtractedData(ctx context.Context, manufacturerID int64, extractedData []*ExtractedEquipmentData) {
	for _, data := range extractedData {
		// Convert to entity and save
		catalog := s.convertToEquipmentCatalog(manufacturerID, data)
		
		_, err := s.equipmentCatalogUc.CreateEquipmentCatalog(ctx, catalog)
		if err != nil {
			s.log.WithContext(ctx).Errorf("Failed to save equipment catalog: %v", err)
		}
	}
}

func (s *EquipmentDatabaseBuilderService) convertToEquipmentCatalog(manufacturerID int64, data *ExtractedEquipmentData) *entity.EquipmentCatalog {
	catalog := &entity.EquipmentCatalog{
		ManufacturerID:       manufacturerID,
		ModelNumber:          data.ModelNumber,
		ProductName:          data.ProductName,
		ProductFamily:        data.ProductFamily,
		Category:             data.Category,
		CoolingCapacityBTU:   data.CoolingCapacityBTU,
		HeatingCapacityBTU:   data.HeatingCapacityBTU,
		SEER:                 data.SEER,
		EER:                  data.EER,
		HSPF:                 data.HSPF,
		COP:                  data.COP,
		Weight:               data.Weight,
		MSRP:                 data.MSRP,
		AHRINumber:           data.AHRINumber,
		EnergyStar:           data.EnergyStar,
		Certifications:       data.Certifications,
		ImageURLs:            data.ImageURLs,
		DocumentURLs:         data.DocumentURLs,
		DataSource:           "crawl4ai",
		SourceURL:            data.SourceURL,
		DataQualityScore:     data.DataQualityScore,
		IsAvailable:          true,
	}

	// Set specifications
	if data.Specifications != nil {
		specs, _ := json.Marshal(data.Specifications)
		catalog.Specifications = specs
	}

	return catalog
}

// Helper functions
func timePtr(t time.Time) *time.Time {
	return &t
}

func int64Ptr(i int64) *int64 {
	return &i
}
