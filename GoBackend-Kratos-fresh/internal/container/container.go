package container

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"go.uber.org/zap"
)

// 🔧 SUSTAINABLE DEPENDENCY INJECTION
// Clean, Go-idiomatic DI without heavy frameworks

type Container struct {
	services map[string]*ServiceDefinition
	instances map[string]interface{}
	logger   *zap.Logger
	mu       sync.RWMutex
}

type ServiceDefinition struct {
	Name         string
	Type         reflect.Type
	Factory      FactoryFunc
	Singleton    bool
	Dependencies []string
	Lifecycle    Lifecycle
	Tags         []string
	Metadata     map[string]interface{}
}

type FactoryFunc func(ctx context.Context, container *Container) (interface{}, error)

type Lifecycle int

const (
	LifecycleSingleton Lifecycle = iota
	LifecycleTransient
	LifecycleScoped
)

func (l Lifecycle) String() string {
	switch l {
	case LifecycleSingleton:
		return "singleton"
	case LifecycleTransient:
		return "transient"
	case LifecycleScoped:
		return "scoped"
	default:
		return "unknown"
	}
}

// ServiceRegistry holds service registration information
type ServiceRegistry struct {
	container *Container
}

// NewContainer creates a new dependency injection container
func NewContainer(logger *zap.Logger) *Container {
	return &Container{
		services:  make(map[string]*ServiceDefinition),
		instances: make(map[string]interface{}),
		logger:    logger,
	}
}

// Registry returns a service registry for fluent registration
func (c *Container) Registry() *ServiceRegistry {
	return &ServiceRegistry{container: c}
}

// Register registers a service with the container
func (sr *ServiceRegistry) Register(name string, factory FactoryFunc) *ServiceBuilder {
	return &ServiceBuilder{
		registry: sr,
		definition: &ServiceDefinition{
			Name:         name,
			Factory:      factory,
			Singleton:    false,
			Dependencies: []string{},
			Lifecycle:    LifecycleTransient,
			Tags:         []string{},
			Metadata:     make(map[string]interface{}),
		},
	}
}

// ServiceBuilder provides fluent API for service configuration
type ServiceBuilder struct {
	registry   *ServiceRegistry
	definition *ServiceDefinition
}

func (sb *ServiceBuilder) AsSingleton() *ServiceBuilder {
	sb.definition.Singleton = true
	sb.definition.Lifecycle = LifecycleSingleton
	return sb
}

func (sb *ServiceBuilder) AsTransient() *ServiceBuilder {
	sb.definition.Singleton = false
	sb.definition.Lifecycle = LifecycleTransient
	return sb
}

func (sb *ServiceBuilder) AsScoped() *ServiceBuilder {
	sb.definition.Singleton = false
	sb.definition.Lifecycle = LifecycleScoped
	return sb
}

func (sb *ServiceBuilder) DependsOn(dependencies ...string) *ServiceBuilder {
	sb.definition.Dependencies = append(sb.definition.Dependencies, dependencies...)
	return sb
}

func (sb *ServiceBuilder) WithTags(tags ...string) *ServiceBuilder {
	sb.definition.Tags = append(sb.definition.Tags, tags...)
	return sb
}

func (sb *ServiceBuilder) WithMetadata(key string, value interface{}) *ServiceBuilder {
	sb.definition.Metadata[key] = value
	return sb
}

func (sb *ServiceBuilder) Build() error {
	// Validate service definition
	if sb.definition.Name == "" {
		return fmt.Errorf("service name cannot be empty")
	}
	if sb.definition.Factory == nil {
		return fmt.Errorf("service factory cannot be nil")
	}

	// Register service
	sb.registry.container.mu.Lock()
	defer sb.registry.container.mu.Unlock()

	sb.registry.container.services[sb.definition.Name] = sb.definition
	
	sb.registry.container.logger.Info("Service registered",
		zap.String("name", sb.definition.Name),
		zap.String("lifecycle", sb.definition.Lifecycle.String()),
		zap.Strings("dependencies", sb.definition.Dependencies),
		zap.Strings("tags", sb.definition.Tags),
	)

	return nil
}

// Resolve resolves a service by name
func (c *Container) Resolve(ctx context.Context, name string) (interface{}, error) {
	c.mu.RLock()
	definition, exists := c.services[name]
	c.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("service '%s' not found", name)
	}

	// Check if singleton instance exists
	if definition.Singleton {
		c.mu.RLock()
		if instance, exists := c.instances[name]; exists {
			c.mu.RUnlock()
			return instance, nil
		}
		c.mu.RUnlock()
	}

	// Resolve dependencies first
	if err := c.resolveDependencies(ctx, definition); err != nil {
		return nil, fmt.Errorf("failed to resolve dependencies for '%s': %w", name, err)
	}

	// Create instance
	instance, err := definition.Factory(ctx, c)
	if err != nil {
		return nil, fmt.Errorf("failed to create instance of '%s': %w", name, err)
	}

	// Store singleton instance
	if definition.Singleton {
		c.mu.Lock()
		c.instances[name] = instance
		c.mu.Unlock()
	}

	c.logger.Debug("Service resolved",
		zap.String("name", name),
		zap.String("type", reflect.TypeOf(instance).String()),
	)

	return instance, nil
}

// MustResolve resolves a service and panics if it fails
func (c *Container) MustResolve(ctx context.Context, name string) interface{} {
	instance, err := c.Resolve(ctx, name)
	if err != nil {
		panic(fmt.Sprintf("failed to resolve service '%s': %v", name, err))
	}
	return instance
}

// ResolveByType resolves a service by type
func (c *Container) ResolveByType(ctx context.Context, serviceType reflect.Type) (interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	for name, definition := range c.services {
		if definition.Type == serviceType {
			return c.Resolve(ctx, name)
		}
	}

	return nil, fmt.Errorf("no service found for type '%s'", serviceType.String())
}

// ResolveByTag resolves all services with a specific tag
func (c *Container) ResolveByTag(ctx context.Context, tag string) ([]interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var instances []interface{}
	for name, definition := range c.services {
		for _, t := range definition.Tags {
			if t == tag {
				instance, err := c.Resolve(ctx, name)
				if err != nil {
					return nil, err
				}
				instances = append(instances, instance)
				break
			}
		}
	}

	return instances, nil
}

// resolveDependencies resolves all dependencies for a service
func (c *Container) resolveDependencies(ctx context.Context, definition *ServiceDefinition) error {
	for _, dep := range definition.Dependencies {
		_, err := c.Resolve(ctx, dep)
		if err != nil {
			return fmt.Errorf("failed to resolve dependency '%s': %w", dep, err)
		}
	}
	return nil
}

// GetServiceInfo returns information about a registered service
func (c *Container) GetServiceInfo(name string) (*ServiceDefinition, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	definition, exists := c.services[name]
	return definition, exists
}

// ListServices returns all registered service names
func (c *Container) ListServices() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var names []string
	for name := range c.services {
		names = append(names, name)
	}
	return names
}

// ListServicesByTag returns service names with a specific tag
func (c *Container) ListServicesByTag(tag string) []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var names []string
	for name, definition := range c.services {
		for _, t := range definition.Tags {
			if t == tag {
				names = append(names, name)
				break
			}
		}
	}
	return names
}

// Validate validates the container configuration
func (c *Container) Validate() error {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// Check for circular dependencies
	for name, definition := range c.services {
		if err := c.checkCircularDependencies(name, definition, make(map[string]bool)); err != nil {
			return err
		}
	}

	// Check if all dependencies exist
	for name, definition := range c.services {
		for _, dep := range definition.Dependencies {
			if _, exists := c.services[dep]; !exists {
				return fmt.Errorf("service '%s' depends on non-existent service '%s'", name, dep)
			}
		}
	}

	return nil
}

// checkCircularDependencies checks for circular dependencies
func (c *Container) checkCircularDependencies(name string, definition *ServiceDefinition, visited map[string]bool) error {
	if visited[name] {
		return fmt.Errorf("circular dependency detected involving service '%s'", name)
	}

	visited[name] = true
	defer delete(visited, name)

	for _, dep := range definition.Dependencies {
		if depDefinition, exists := c.services[dep]; exists {
			if err := c.checkCircularDependencies(dep, depDefinition, visited); err != nil {
				return err
			}
		}
	}

	return nil
}

// Dispose disposes of all singleton instances
func (c *Container) Dispose() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	for name, instance := range c.instances {
		// Check if instance implements Disposable interface
		if disposable, ok := instance.(Disposable); ok {
			if err := disposable.Dispose(); err != nil {
				c.logger.Error("Failed to dispose service",
					zap.String("name", name),
					zap.Error(err),
				)
			}
		}
	}

	// Clear instances
	c.instances = make(map[string]interface{})
	
	c.logger.Info("Container disposed")
	return nil
}

// Disposable interface for services that need cleanup
type Disposable interface {
	Dispose() error
}

// Health check for container
func (c *Container) HealthCheck(ctx context.Context) error {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// Check if all singleton services are healthy
	for name, instance := range c.instances {
		if healthChecker, ok := instance.(HealthChecker); ok {
			if err := healthChecker.HealthCheck(ctx); err != nil {
				return fmt.Errorf("service '%s' health check failed: %w", name, err)
			}
		}
	}

	return nil
}

// HealthChecker interface for services that support health checks
type HealthChecker interface {
	HealthCheck(ctx context.Context) error
}

// ServiceStats returns statistics about the container
type ServiceStats struct {
	TotalServices     int            `json:"total_services"`
	SingletonServices int            `json:"singleton_services"`
	TransientServices int            `json:"transient_services"`
	ScopedServices    int            `json:"scoped_services"`
	ActiveInstances   int            `json:"active_instances"`
	ServicesByTag     map[string]int `json:"services_by_tag"`
}

func (c *Container) GetStats() ServiceStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := ServiceStats{
		TotalServices:   len(c.services),
		ActiveInstances: len(c.instances),
		ServicesByTag:   make(map[string]int),
	}

	for _, definition := range c.services {
		switch definition.Lifecycle {
		case LifecycleSingleton:
			stats.SingletonServices++
		case LifecycleTransient:
			stats.TransientServices++
		case LifecycleScoped:
			stats.ScopedServices++
		}

		for _, tag := range definition.Tags {
			stats.ServicesByTag[tag]++
		}
	}

	return stats
}
