package hvac

import (
	"time"
)

// 🏗️ CLEAN ARCHITECTURE - D<PERSON>AIN ENTITIES
// Core business entities for HVAC CRM system

// Customer represents a customer in the HVAC business
type Customer struct {
	ID          CustomerID    `json:"id"`
	Name        string        `json:"name"`
	Email       Email         `json:"email"`
	Phone       Phone         `json:"phone"`
	Address     Address       `json:"address"`
	Status      CustomerStatus `json:"status"`
	Priority    Priority      `json:"priority"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
	
	// Business logic fields
	lifecycle   CustomerLifecycle
	preferences CustomerPreferences
	metadata    CustomerMetadata
}

// CustomerID represents a unique customer identifier
type CustomerID struct {
	value int64
}

func NewCustomerID(id int64) CustomerID {
	return CustomerID{value: id}
}

func (c CustomerID) Value() int64 {
	return c.value
}

func (c CustomerID) String() string {
	return fmt.Sprintf("customer_%d", c.value)
}

// Email represents a validated email address
type Email struct {
	value string
}

func NewEmail(email string) (Email, error) {
	if !isValidEmail(email) {
		return Email{}, ErrInvalidEmail
	}
	return Email{value: email}, nil
}

func (e Email) Value() string {
	return e.value
}

func (e Email) Domain() string {
	parts := strings.Split(e.value, "@")
	if len(parts) == 2 {
		return parts[1]
	}
	return ""
}

// Phone represents a validated phone number
type Phone struct {
	value   string
	country string
}

func NewPhone(phone, country string) (Phone, error) {
	if !isValidPhone(phone) {
		return Phone{}, ErrInvalidPhone
	}
	return Phone{value: phone, country: country}, nil
}

func (p Phone) Value() string {
	return p.value
}

func (p Phone) Formatted() string {
	// Format phone number for display
	return formatPhone(p.value, p.country)
}

// Address represents a customer address
type Address struct {
	Street     string `json:"street"`
	City       string `json:"city"`
	PostalCode string `json:"postal_code"`
	Country    string `json:"country"`
	
	// HVAC-specific fields
	ServiceZone string     `json:"service_zone"`
	Coordinates *LatLng    `json:"coordinates,omitempty"`
	AccessNotes string     `json:"access_notes"`
}

type LatLng struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// CustomerStatus represents the status of a customer
type CustomerStatus int

const (
	CustomerStatusLead CustomerStatus = iota
	CustomerStatusProspect
	CustomerStatusActive
	CustomerStatusInactive
	CustomerStatusVIP
	CustomerStatusChurned
)

func (cs CustomerStatus) String() string {
	switch cs {
	case CustomerStatusLead:
		return "lead"
	case CustomerStatusProspect:
		return "prospect"
	case CustomerStatusActive:
		return "active"
	case CustomerStatusInactive:
		return "inactive"
	case CustomerStatusVIP:
		return "vip"
	case CustomerStatusChurned:
		return "churned"
	default:
		return "unknown"
	}
}

// Priority represents customer priority level
type Priority int

const (
	PriorityLow Priority = iota + 1
	PriorityNormal
	PriorityHigh
	PriorityUrgent
	PriorityCritical
)

func (p Priority) String() string {
	switch p {
	case PriorityLow:
		return "low"
	case PriorityNormal:
		return "normal"
	case PriorityHigh:
		return "high"
	case PriorityUrgent:
		return "urgent"
	case PriorityCritical:
		return "critical"
	default:
		return "normal"
	}
}

// Equipment represents HVAC equipment
type Equipment struct {
	ID           EquipmentID   `json:"id"`
	CustomerID   CustomerID    `json:"customer_id"`
	Type         EquipmentType `json:"type"`
	Brand        string        `json:"brand"`
	Model        string        `json:"model"`
	SerialNumber string        `json:"serial_number"`
	InstallDate  time.Time     `json:"install_date"`
	WarrantyEnd  *time.Time    `json:"warranty_end,omitempty"`
	Status       EquipmentStatus `json:"status"`
	Location     string        `json:"location"`
	Specifications map[string]interface{} `json:"specifications"`
	
	// Business logic
	healthScore  HealthScore
	maintenance  MaintenanceSchedule
	performance  PerformanceMetrics
}

type EquipmentID struct {
	value int64
}

func NewEquipmentID(id int64) EquipmentID {
	return EquipmentID{value: id}
}

func (e EquipmentID) Value() int64 {
	return e.value
}

type EquipmentType int

const (
	EquipmentTypeAirConditioner EquipmentType = iota
	EquipmentTypeHeatPump
	EquipmentTypeFurnace
	EquipmentTypeBoiler
	EquipmentTypeVentilation
	EquipmentTypeThermostat
)

func (et EquipmentType) String() string {
	switch et {
	case EquipmentTypeAirConditioner:
		return "air_conditioner"
	case EquipmentTypeHeatPump:
		return "heat_pump"
	case EquipmentTypeFurnace:
		return "furnace"
	case EquipmentTypeBoiler:
		return "boiler"
	case EquipmentTypeVentilation:
		return "ventilation"
	case EquipmentTypeThermostat:
		return "thermostat"
	default:
		return "unknown"
	}
}

type EquipmentStatus int

const (
	EquipmentStatusOperational EquipmentStatus = iota
	EquipmentStatusMaintenance
	EquipmentStatusRepair
	EquipmentStatusReplacement
	EquipmentStatusDecommissioned
)

func (es EquipmentStatus) String() string {
	switch es {
	case EquipmentStatusOperational:
		return "operational"
	case EquipmentStatusMaintenance:
		return "maintenance"
	case EquipmentStatusRepair:
		return "repair"
	case EquipmentStatusReplacement:
		return "replacement"
	case EquipmentStatusDecommissioned:
		return "decommissioned"
	default:
		return "unknown"
	}
}

// ServiceOrder represents a service order
type ServiceOrder struct {
	ID          ServiceOrderID `json:"id"`
	CustomerID  CustomerID     `json:"customer_id"`
	EquipmentID *EquipmentID   `json:"equipment_id,omitempty"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Type        ServiceType    `json:"type"`
	Status      ServiceStatus  `json:"status"`
	Priority    Priority       `json:"priority"`
	ScheduledAt time.Time      `json:"scheduled_at"`
	CompletedAt *time.Time     `json:"completed_at,omitempty"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	
	// Business logic
	technician   *Technician
	checklist    ServiceChecklist
	parts        []ServicePart
	costs        ServiceCosts
}

type ServiceOrderID struct {
	value int64
}

func NewServiceOrderID(id int64) ServiceOrderID {
	return ServiceOrderID{value: id}
}

func (s ServiceOrderID) Value() int64 {
	return s.value
}

type ServiceType int

const (
	ServiceTypeInstallation ServiceType = iota
	ServiceTypeMaintenance
	ServiceTypeRepair
	ServiceTypeInspection
	ServiceTypeEmergency
	ServiceTypeUpgrade
)

func (st ServiceType) String() string {
	switch st {
	case ServiceTypeInstallation:
		return "installation"
	case ServiceTypeMaintenance:
		return "maintenance"
	case ServiceTypeRepair:
		return "repair"
	case ServiceTypeInspection:
		return "inspection"
	case ServiceTypeEmergency:
		return "emergency"
	case ServiceTypeUpgrade:
		return "upgrade"
	default:
		return "unknown"
	}
}

type ServiceStatus int

const (
	ServiceStatusScheduled ServiceStatus = iota
	ServiceStatusInProgress
	ServiceStatusCompleted
	ServiceStatusCancelled
	ServiceStatusOnHold
)

func (ss ServiceStatus) String() string {
	switch ss {
	case ServiceStatusScheduled:
		return "scheduled"
	case ServiceStatusInProgress:
		return "in_progress"
	case ServiceStatusCompleted:
		return "completed"
	case ServiceStatusCancelled:
		return "cancelled"
	case ServiceStatusOnHold:
		return "on_hold"
	default:
		return "unknown"
	}
}

// Business Logic Types
type CustomerLifecycle struct {
	Stage           LifecycleStage `json:"stage"`
	DaysInStage     int           `json:"days_in_stage"`
	LastInteraction time.Time     `json:"last_interaction"`
	TouchpointCount int           `json:"touchpoint_count"`
}

type LifecycleStage int

const (
	LifecycleStageLead LifecycleStage = iota
	LifecycleStageProspect
	LifecycleStageCustomer
	LifecycleStageLoyalCustomer
	LifecycleStageAtRisk
	LifecycleStageChurned
)

type CustomerPreferences struct {
	PreferredContactMethod ContactMethod `json:"preferred_contact_method"`
	PreferredTimeSlots     []TimeSlot    `json:"preferred_time_slots"`
	ServiceReminders       bool          `json:"service_reminders"`
	MarketingOptIn         bool          `json:"marketing_opt_in"`
}

type ContactMethod int

const (
	ContactMethodEmail ContactMethod = iota
	ContactMethodPhone
	ContactMethodSMS
	ContactMethodApp
)

type TimeSlot struct {
	DayOfWeek int `json:"day_of_week"` // 0 = Sunday
	StartHour int `json:"start_hour"`
	EndHour   int `json:"end_hour"`
}

type CustomerMetadata struct {
	Source         string            `json:"source"`
	Campaign       string            `json:"campaign,omitempty"`
	Tags           []string          `json:"tags"`
	CustomFields   map[string]interface{} `json:"custom_fields"`
	Notes          []CustomerNote    `json:"notes"`
}

type CustomerNote struct {
	ID        int64     `json:"id"`
	Content   string    `json:"content"`
	AuthorID  int64     `json:"author_id"`
	CreatedAt time.Time `json:"created_at"`
	Type      NoteType  `json:"type"`
}

type NoteType int

const (
	NoteTypeGeneral NoteType = iota
	NoteTypeService
	NoteTypeSales
	NoteTypeComplaint
	NoteTypeCompliment
)

// Domain Errors
var (
	ErrInvalidEmail       = errors.New("invalid email address")
	ErrInvalidPhone       = errors.New("invalid phone number")
	ErrCustomerNotFound   = errors.New("customer not found")
	ErrEquipmentNotFound  = errors.New("equipment not found")
	ErrServiceOrderNotFound = errors.New("service order not found")
	ErrInvalidStatus      = errors.New("invalid status")
	ErrUnauthorized       = errors.New("unauthorized access")
)

// Helper functions
func isValidEmail(email string) bool {
	// Implement email validation
	return strings.Contains(email, "@") && len(email) > 3
}

func isValidPhone(phone string) bool {
	// Implement phone validation
	return len(phone) >= 9
}

func formatPhone(phone, country string) string {
	// Implement phone formatting
	return phone
}
