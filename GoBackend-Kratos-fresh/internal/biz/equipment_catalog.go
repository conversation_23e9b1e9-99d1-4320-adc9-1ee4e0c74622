package biz

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/entity"
)

// 🏭 Equipment Catalog Business Logic
// Automated HVAC Equipment Database Management

// EquipmentCatalogRepo defines the repository interface for equipment catalog
type EquipmentCatalogRepo interface {
	// Manufacturer operations
	CreateManufacturer(ctx context.Context, manufacturer *entity.Manufacturer) (*entity.Manufacturer, error)
	GetManufacturer(ctx context.Context, id int64) (*entity.Manufacturer, error)
	GetManufacturerByName(ctx context.Context, name string) (*entity.Manufacturer, error)
	ListManufacturers(ctx context.Context, page, pageSize int32, isActive *bool) ([]*entity.Manufacturer, int32, error)
	UpdateManufacturer(ctx context.Context, manufacturer *entity.Manufacturer) (*entity.Manufacturer, error)
	DeleteManufacturer(ctx context.Context, id int64) error

	// Equipment catalog operations
	CreateEquipmentCatalog(ctx context.Context, catalog *entity.EquipmentCatalog) (*entity.EquipmentCatalog, error)
	GetEquipmentCatalog(ctx context.Context, id int64) (*entity.EquipmentCatalog, error)
	GetEquipmentCatalogByModel(ctx context.Context, manufacturerID int64, modelNumber string) (*entity.EquipmentCatalog, error)
	ListEquipmentCatalog(ctx context.Context, req *ListEquipmentCatalogRequest) ([]*entity.EquipmentCatalog, int32, error)
	UpdateEquipmentCatalog(ctx context.Context, catalog *entity.EquipmentCatalog) (*entity.EquipmentCatalog, error)
	DeleteEquipmentCatalog(ctx context.Context, id int64) error
	SearchEquipmentCatalog(ctx context.Context, req *SearchEquipmentCatalogRequest) ([]*entity.EquipmentCatalog, int32, error)

	// Catalog image operations
	CreateCatalogImage(ctx context.Context, image *entity.CatalogImage) (*entity.CatalogImage, error)
	GetCatalogImages(ctx context.Context, catalogID int64) ([]*entity.CatalogImage, error)
	DeleteCatalogImage(ctx context.Context, id int64) error

	// Equipment compatibility operations
	CreateEquipmentCompatibility(ctx context.Context, compatibility *entity.EquipmentCompatibility) (*entity.EquipmentCompatibility, error)
	GetEquipmentCompatibility(ctx context.Context, equipmentID int64) ([]*entity.EquipmentCompatibility, error)
	GetCompatibleEquipment(ctx context.Context, catalogID int64) ([]*entity.EquipmentCompatibility, error)
	UpdateCompatibilityScore(ctx context.Context, id int64, score float64) error

	// Crawl job operations
	CreateCrawlJob(ctx context.Context, job *entity.CrawlJob) (*entity.CrawlJob, error)
	GetCrawlJob(ctx context.Context, id int64) (*entity.CrawlJob, error)
	ListCrawlJobs(ctx context.Context, manufacturerID int64, status string, page, pageSize int32) ([]*entity.CrawlJob, int32, error)
	UpdateCrawlJob(ctx context.Context, job *entity.CrawlJob) (*entity.CrawlJob, error)
	GetPendingCrawlJobs(ctx context.Context, limit int32) ([]*entity.CrawlJob, error)
}

// EquipmentCatalogUsecase defines the business logic for equipment catalog
type EquipmentCatalogUsecase struct {
	repo EquipmentCatalogRepo
	log  *log.Helper
}

// Request/Response types
type ListEquipmentCatalogRequest struct {
	Page           int32    `json:"page"`
	PageSize       int32    `json:"page_size"`
	ManufacturerID *int64   `json:"manufacturer_id,omitempty"`
	Category       string   `json:"category,omitempty"`
	SubCategory    string   `json:"sub_category,omitempty"`
	IsAvailable    *bool    `json:"is_available,omitempty"`
	MinSEER        *float64 `json:"min_seer,omitempty"`
	MaxSEER        *float64 `json:"max_seer,omitempty"`
	MinCapacity    *int64   `json:"min_capacity,omitempty"`
	MaxCapacity    *int64   `json:"max_capacity,omitempty"`
	EnergyStar     *bool    `json:"energy_star,omitempty"`
	SortBy         string   `json:"sort_by,omitempty"` // seer, capacity, price, name
	SortOrder      string   `json:"sort_order,omitempty"` // asc, desc
}

type SearchEquipmentCatalogRequest struct {
	Query          string   `json:"query"`
	Page           int32    `json:"page"`
	PageSize       int32    `json:"page_size"`
	ManufacturerID *int64   `json:"manufacturer_id,omitempty"`
	Category       string   `json:"category,omitempty"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
}

// Business logic errors
var (
	ErrManufacturerNotFound      = fmt.Errorf("manufacturer not found")
	ErrManufacturerNameExists    = fmt.Errorf("manufacturer name already exists")
	ErrEquipmentCatalogNotFound  = fmt.Errorf("equipment catalog not found")
	ErrEquipmentModelExists      = fmt.Errorf("equipment model already exists for manufacturer")
	ErrCrawlJobNotFound          = fmt.Errorf("crawl job not found")
	ErrInvalidManufacturerID     = fmt.Errorf("invalid manufacturer ID")
	ErrInvalidCatalogID          = fmt.Errorf("invalid catalog ID")
	ErrInvalidDataQualityScore   = fmt.Errorf("data quality score must be between 0.0 and 1.0")
	ErrInvalidCompatibilityScore = fmt.Errorf("compatibility score must be between 0.0 and 1.0")
)

// NewEquipmentCatalogUsecase creates a new equipment catalog usecase
func NewEquipmentCatalogUsecase(repo EquipmentCatalogRepo, logger log.Logger) *EquipmentCatalogUsecase {
	return &EquipmentCatalogUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// MANUFACTURER OPERATIONS
// ============================================================================

// CreateManufacturer creates a new manufacturer
func (uc *EquipmentCatalogUsecase) CreateManufacturer(ctx context.Context, manufacturer *entity.Manufacturer) (*entity.Manufacturer, error) {
	uc.log.WithContext(ctx).Infof("Creating manufacturer: %s", manufacturer.Name)

	// Validate manufacturer data
	if manufacturer.Name == "" {
		return nil, fmt.Errorf("manufacturer name is required")
	}

	// Check if manufacturer already exists
	existing, err := uc.repo.GetManufacturerByName(ctx, manufacturer.Name)
	if err == nil && existing != nil {
		return nil, ErrManufacturerNameExists
	}

	// Set defaults
	if manufacturer.CrawlFrequency == 0 {
		manufacturer.CrawlFrequency = 24 // Default to 24 hours
	}

	return uc.repo.CreateManufacturer(ctx, manufacturer)
}

// GetManufacturer retrieves a manufacturer by ID
func (uc *EquipmentCatalogUsecase) GetManufacturer(ctx context.Context, id int64) (*entity.Manufacturer, error) {
	if id <= 0 {
		return nil, ErrInvalidManufacturerID
	}

	return uc.repo.GetManufacturer(ctx, id)
}

// ListManufacturers retrieves manufacturers with filtering
func (uc *EquipmentCatalogUsecase) ListManufacturers(ctx context.Context, page, pageSize int32, isActive *bool) ([]*entity.Manufacturer, int32, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListManufacturers(ctx, page, pageSize, isActive)
}

// UpdateManufacturer updates an existing manufacturer
func (uc *EquipmentCatalogUsecase) UpdateManufacturer(ctx context.Context, manufacturer *entity.Manufacturer) (*entity.Manufacturer, error) {
	uc.log.WithContext(ctx).Infof("Updating manufacturer: %d", manufacturer.ID)

	// Validate manufacturer exists
	existing, err := uc.repo.GetManufacturer(ctx, manufacturer.ID)
	if err != nil {
		return nil, err
	}
	if existing == nil {
		return nil, ErrManufacturerNotFound
	}

	return uc.repo.UpdateManufacturer(ctx, manufacturer)
}

// ============================================================================
// EQUIPMENT CATALOG OPERATIONS
// ============================================================================

// CreateEquipmentCatalog creates a new equipment catalog entry
func (uc *EquipmentCatalogUsecase) CreateEquipmentCatalog(ctx context.Context, catalog *entity.EquipmentCatalog) (*entity.EquipmentCatalog, error) {
	uc.log.WithContext(ctx).Infof("Creating equipment catalog: %s %s", catalog.ModelNumber, catalog.ProductName)

	// Validate catalog data
	if err := uc.validateEquipmentCatalog(catalog); err != nil {
		return nil, err
	}

	// Check if model already exists for manufacturer
	existing, err := uc.repo.GetEquipmentCatalogByModel(ctx, catalog.ManufacturerID, catalog.ModelNumber)
	if err == nil && existing != nil {
		return nil, ErrEquipmentModelExists
	}

	// Set defaults
	if catalog.DataQualityScore == 0 {
		catalog.DataQualityScore = 0.5 // Default quality score
	}

	return uc.repo.CreateEquipmentCatalog(ctx, catalog)
}

// GetEquipmentCatalog retrieves equipment catalog by ID
func (uc *EquipmentCatalogUsecase) GetEquipmentCatalog(ctx context.Context, id int64) (*entity.EquipmentCatalog, error) {
	if id <= 0 {
		return nil, ErrInvalidCatalogID
	}

	return uc.repo.GetEquipmentCatalog(ctx, id)
}

// ListEquipmentCatalog retrieves equipment catalog with filtering and pagination
func (uc *EquipmentCatalogUsecase) ListEquipmentCatalog(ctx context.Context, req *ListEquipmentCatalogRequest) ([]*entity.EquipmentCatalog, int32, error) {
	// Validate and set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	return uc.repo.ListEquipmentCatalog(ctx, req)
}

// SearchEquipmentCatalog performs full-text search on equipment catalog
func (uc *EquipmentCatalogUsecase) SearchEquipmentCatalog(ctx context.Context, req *SearchEquipmentCatalogRequest) ([]*entity.EquipmentCatalog, int32, error) {
	// Validate and set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}
	if req.Query == "" {
		return nil, 0, fmt.Errorf("search query is required")
	}

	return uc.repo.SearchEquipmentCatalog(ctx, req)
}

// UpdateEquipmentCatalog updates an existing equipment catalog entry
func (uc *EquipmentCatalogUsecase) UpdateEquipmentCatalog(ctx context.Context, catalog *entity.EquipmentCatalog) (*entity.EquipmentCatalog, error) {
	uc.log.WithContext(ctx).Infof("Updating equipment catalog: %d", catalog.ID)

	// Validate catalog data
	if err := uc.validateEquipmentCatalog(catalog); err != nil {
		return nil, err
	}

	// Validate catalog exists
	existing, err := uc.repo.GetEquipmentCatalog(ctx, catalog.ID)
	if err != nil {
		return nil, err
	}
	if existing == nil {
		return nil, ErrEquipmentCatalogNotFound
	}

	return uc.repo.UpdateEquipmentCatalog(ctx, catalog)
}

// ============================================================================
// CRAWL JOB OPERATIONS
// ============================================================================

// CreateCrawlJob creates a new crawl job
func (uc *EquipmentCatalogUsecase) CreateCrawlJob(ctx context.Context, job *entity.CrawlJob) (*entity.CrawlJob, error) {
	uc.log.WithContext(ctx).Infof("Creating crawl job for manufacturer: %d", job.ManufacturerID)

	// Validate job data
	if job.ManufacturerID <= 0 {
		return nil, ErrInvalidManufacturerID
	}
	if job.JobType == "" {
		return nil, fmt.Errorf("job type is required")
	}
	if job.Status == "" {
		job.Status = "pending"
	}

	// Validate manufacturer exists
	manufacturer, err := uc.repo.GetManufacturer(ctx, job.ManufacturerID)
	if err != nil {
		return nil, err
	}
	if manufacturer == nil {
		return nil, ErrManufacturerNotFound
	}

	return uc.repo.CreateCrawlJob(ctx, job)
}

// GetCrawlJob retrieves a crawl job by ID
func (uc *EquipmentCatalogUsecase) GetCrawlJob(ctx context.Context, id int64) (*entity.CrawlJob, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid crawl job ID")
	}

	return uc.repo.GetCrawlJob(ctx, id)
}

// UpdateCrawlJob updates an existing crawl job
func (uc *EquipmentCatalogUsecase) UpdateCrawlJob(ctx context.Context, job *entity.CrawlJob) (*entity.CrawlJob, error) {
	return uc.repo.UpdateCrawlJob(ctx, job)
}

// GetPendingCrawlJobs retrieves pending crawl jobs for processing
func (uc *EquipmentCatalogUsecase) GetPendingCrawlJobs(ctx context.Context, limit int32) ([]*entity.CrawlJob, error) {
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	return uc.repo.GetPendingCrawlJobs(ctx, limit)
}

// ============================================================================
// HELPER METHODS
// ============================================================================

// validateEquipmentCatalog validates equipment catalog data
func (uc *EquipmentCatalogUsecase) validateEquipmentCatalog(catalog *entity.EquipmentCatalog) error {
	if catalog.ManufacturerID <= 0 {
		return ErrInvalidManufacturerID
	}
	if catalog.ModelNumber == "" {
		return fmt.Errorf("model number is required")
	}
	if catalog.ProductName == "" {
		return fmt.Errorf("product name is required")
	}
	if catalog.Category == "" {
		return fmt.Errorf("category is required")
	}
	if catalog.DataQualityScore < 0.0 || catalog.DataQualityScore > 1.0 {
		return ErrInvalidDataQualityScore
	}

	return nil
}
