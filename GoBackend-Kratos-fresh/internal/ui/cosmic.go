package ui

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// 🌟 COSMIC UI COMPONENTS
// Golden ratio-based, sustainable, beautiful interface components

const (
	GoldenRatio = 1.618
	CosmicBase  = 8.0 // Base spacing unit
)

// CosmicComponent represents a cosmic-level UI component
type CosmicComponent struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Classes     []string               `json:"classes"`
	Attributes  map[string]interface{} `json:"attributes"`
	Content     interface{}            `json:"content"`
	Children    []CosmicComponent      `json:"children,omitempty"`
	Animation   *Animation             `json:"animation,omitempty"`
	Responsive  *ResponsiveConfig      `json:"responsive,omitempty"`
	Accessibility *AccessibilityConfig `json:"accessibility,omitempty"`
}

type Animation struct {
	Type        string  `json:"type"`        // fadeIn, slideIn, scaleIn, cosmic
	Duration    float64 `json:"duration"`    // seconds
	Delay       float64 `json:"delay"`       // seconds
	Easing      string  `json:"easing"`      // ease, ease-in, ease-out, cubic-bezier
	Infinite    bool    `json:"infinite"`
	Direction   string  `json:"direction"`   // normal, reverse, alternate
	FillMode    string  `json:"fill_mode"`   // forwards, backwards, both
}

type ResponsiveConfig struct {
	Mobile  *ComponentConfig `json:"mobile,omitempty"`
	Tablet  *ComponentConfig `json:"tablet,omitempty"`
	Desktop *ComponentConfig `json:"desktop,omitempty"`
}

type ComponentConfig struct {
	Classes    []string               `json:"classes,omitempty"`
	Attributes map[string]interface{} `json:"attributes,omitempty"`
	Hidden     bool                   `json:"hidden,omitempty"`
}

type AccessibilityConfig struct {
	Role        string `json:"role,omitempty"`
	AriaLabel   string `json:"aria_label,omitempty"`
	AriaDescribedBy string `json:"aria_described_by,omitempty"`
	TabIndex    int    `json:"tab_index,omitempty"`
	Focusable   bool   `json:"focusable"`
}

// CosmicCard creates a beautiful card component
func CosmicCard(title, content string, options ...CardOption) CosmicComponent {
	card := CosmicComponent{
		ID:   generateID("card"),
		Type: "card",
		Classes: []string{
			"card",
			"cosmic-card",
			"shadow-cosmic",
			"border-0",
			"rounded-cosmic",
		},
		Attributes: map[string]interface{}{
			"data-cosmic": "true",
		},
		Animation: &Animation{
			Type:     "fadeIn",
			Duration: 0.6,
			Easing:   "ease-out",
			FillMode: "forwards",
		},
		Accessibility: &AccessibilityConfig{
			Role:      "article",
			Focusable: true,
			TabIndex:  0,
		},
	}

	// Apply options
	for _, option := range options {
		option(&card)
	}

	// Create card structure
	header := CosmicComponent{
		Type: "div",
		Classes: []string{"card-header", "cosmic-header", "border-0"},
		Content: fmt.Sprintf(`<h5 class="card-title mb-0 cosmic-title">%s</h5>`, title),
	}

	body := CosmicComponent{
		Type: "div",
		Classes: []string{"card-body", "cosmic-body"},
		Content: content,
	}

	card.Children = []CosmicComponent{header, body}

	return card
}

type CardOption func(*CosmicComponent)

func WithCardColor(color string) CardOption {
	return func(c *CosmicComponent) {
		c.Classes = append(c.Classes, fmt.Sprintf("border-%s", color))
		c.Attributes["data-color"] = color
	}
}

func WithCardSize(size string) CardOption {
	return func(c *CosmicComponent) {
		c.Classes = append(c.Classes, fmt.Sprintf("card-%s", size))
		c.Attributes["data-size"] = size
	}
}

func WithCardAnimation(animType string, duration float64) CardOption {
	return func(c *CosmicComponent) {
		c.Animation = &Animation{
			Type:     animType,
			Duration: duration,
			Easing:   "cubic-bezier(0.4, 0, 0.2, 1)",
			FillMode: "forwards",
		}
	}
}

// CosmicButton creates a beautiful button component
func CosmicButton(text, variant string, options ...ButtonOption) CosmicComponent {
	button := CosmicComponent{
		ID:   generateID("btn"),
		Type: "button",
		Classes: []string{
			"btn",
			fmt.Sprintf("btn-%s", variant),
			"cosmic-btn",
			"rounded-cosmic",
			"shadow-cosmic-sm",
		},
		Attributes: map[string]interface{}{
			"type":        "button",
			"data-cosmic": "true",
		},
		Content: text,
		Animation: &Animation{
			Type:     "scaleIn",
			Duration: 0.3,
			Easing:   "ease-out",
		},
		Accessibility: &AccessibilityConfig{
			Role:      "button",
			Focusable: true,
			TabIndex:  0,
		},
	}

	// Apply options
	for _, option := range options {
		option(&button)
	}

	return button
}

type ButtonOption func(*CosmicComponent)

func WithButtonIcon(icon string) ButtonOption {
	return func(c *CosmicComponent) {
		content := fmt.Sprintf(`<i class="%s me-2"></i>%s`, icon, c.Content)
		c.Content = content
	}
}

func WithButtonSize(size string) ButtonOption {
	return func(c *CosmicComponent) {
		c.Classes = append(c.Classes, fmt.Sprintf("btn-%s", size))
	}
}

func WithButtonAction(action, url string) ButtonOption {
	return func(c *CosmicComponent) {
		if url != "" {
			c.Attributes["data-url"] = url
		}
		if action != "" {
			c.Attributes["data-action"] = action
		}
	}
}

// CosmicKPI creates a beautiful KPI component
func CosmicKPI(title, value, change string, options ...KPIOption) CosmicComponent {
	kpi := CosmicComponent{
		ID:   generateID("kpi"),
		Type: "div",
		Classes: []string{
			"cosmic-kpi",
			"p-4",
			"rounded-cosmic",
			"bg-white",
			"shadow-cosmic",
			"border-start",
			"border-4",
			"border-primary",
		},
		Animation: &Animation{
			Type:     "slideIn",
			Duration: 0.8,
			Easing:   "ease-out",
			FillMode: "forwards",
		},
		Accessibility: &AccessibilityConfig{
			Role:      "region",
			AriaLabel: fmt.Sprintf("KPI: %s", title),
		},
	}

	// Apply options
	for _, option := range options {
		option(&kpi)
	}

	// Create KPI structure
	titleEl := CosmicComponent{
		Type:    "div",
		Classes: []string{"cosmic-kpi-title", "text-muted", "small", "mb-2"},
		Content: title,
	}

	valueEl := CosmicComponent{
		Type:    "div",
		Classes: []string{"cosmic-kpi-value", "h3", "mb-2", "fw-bold"},
		Content: value,
	}

	changeEl := CosmicComponent{
		Type:    "div",
		Classes: []string{"cosmic-kpi-change", "small"},
		Content: change,
	}

	kpi.Children = []CosmicComponent{titleEl, valueEl, changeEl}

	return kpi
}

type KPIOption func(*CosmicComponent)

func WithKPIColor(color string) KPIOption {
	return func(c *CosmicComponent) {
		// Update border color
		for i, class := range c.Classes {
			if strings.HasPrefix(class, "border-") && class != "border-start" && class != "border-4" {
				c.Classes[i] = fmt.Sprintf("border-%s", color)
				break
			}
		}
		if !containsClass(c.Classes, fmt.Sprintf("border-%s", color)) {
			c.Classes = append(c.Classes, fmt.Sprintf("border-%s", color))
		}
	}
}

func WithKPIIcon(icon string) KPIOption {
	return func(c *CosmicComponent) {
		c.Attributes["data-icon"] = icon
	}
}

// CosmicSpacing calculates cosmic spacing based on golden ratio
func CosmicSpacing(level int) float64 {
	return CosmicBase * math.Pow(GoldenRatio, float64(level))
}

// CosmicGrid creates a responsive grid system
func CosmicGrid(columns int, gap string) CosmicComponent {
	return CosmicComponent{
		ID:   generateID("grid"),
		Type: "div",
		Classes: []string{
			"cosmic-grid",
			"d-grid",
			fmt.Sprintf("gap-%s", gap),
		},
		Attributes: map[string]interface{}{
			"style":       fmt.Sprintf("grid-template-columns: repeat(%d, 1fr);", columns),
			"data-cosmic": "true",
		},
		Responsive: &ResponsiveConfig{
			Mobile: &ComponentConfig{
				Attributes: map[string]interface{}{
					"style": "grid-template-columns: 1fr;",
				},
			},
			Tablet: &ComponentConfig{
				Attributes: map[string]interface{}{
					"style": fmt.Sprintf("grid-template-columns: repeat(%d, 1fr);", min(columns, 2)),
				},
			},
		},
	}
}

// CosmicNavigation creates a beautiful navigation component
func CosmicNavigation(items []NavigationItem) CosmicComponent {
	nav := CosmicComponent{
		ID:   generateID("nav"),
		Type: "nav",
		Classes: []string{
			"cosmic-nav",
			"navbar",
			"navbar-expand-lg",
			"navbar-dark",
			"bg-cosmic-primary",
		},
		Attributes: map[string]interface{}{
			"data-cosmic": "true",
		},
		Accessibility: &AccessibilityConfig{
			Role:      "navigation",
			AriaLabel: "Main navigation",
		},
	}

	// Create navigation items
	var navItems []CosmicComponent
	for _, item := range items {
		navItem := CosmicComponent{
			Type: "li",
			Classes: []string{"nav-item"},
			Content: fmt.Sprintf(`<a class="nav-link cosmic-nav-link" href="%s">
				<i class="%s me-2"></i>%s
				%s
			</a>`, item.URL, item.Icon, item.Label, formatBadge(item.Badge)),
		}
		navItems = append(navItems, navItem)
	}

	navList := CosmicComponent{
		Type:     "ul",
		Classes:  []string{"navbar-nav", "cosmic-nav-list"},
		Children: navItems,
	}

	nav.Children = []CosmicComponent{navList}

	return nav
}

type NavigationItem struct {
	Label string
	URL   string
	Icon  string
	Badge string
}

// Helper functions
func generateID(prefix string) string {
	return fmt.Sprintf("%s_%d", prefix, time.Now().UnixNano())
}

func containsClass(classes []string, class string) bool {
	for _, c := range classes {
		if c == class {
			return true
		}
	}
	return false
}

func formatBadge(badge string) string {
	if badge == "" {
		return ""
	}
	return fmt.Sprintf(`<span class="badge bg-danger cosmic-badge">%s</span>`, badge)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// CosmicCSS generates cosmic-level CSS
func CosmicCSS() string {
	return `
/* 🌟 COSMIC UI STYLES */
:root {
    --cosmic-primary: #2c3e50;
    --cosmic-secondary: #3498db;
    --cosmic-success: #27ae60;
    --cosmic-warning: #f39c12;
    --cosmic-danger: #e74c3c;
    --cosmic-info: #17a2b8;
    --cosmic-light: #f8f9fa;
    --cosmic-dark: #343a40;
    --cosmic-golden-ratio: 1.618;
    --cosmic-base-spacing: 8px;
    --cosmic-border-radius: calc(var(--cosmic-base-spacing) * var(--cosmic-golden-ratio));
    --cosmic-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --cosmic-shadow-sm: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
    --cosmic-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.cosmic-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--cosmic-border-radius);
}

.cosmic-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--cosmic-shadow-lg);
}

.cosmic-btn {
    transition: all 0.2s ease;
    border-radius: calc(var(--cosmic-base-spacing) * 0.5);
    font-weight: 500;
}

.cosmic-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--cosmic-shadow);
}

.cosmic-kpi {
    transition: all 0.3s ease;
}

.cosmic-kpi:hover {
    transform: scale(1.02);
}

.cosmic-nav-link {
    transition: all 0.2s ease;
    border-radius: calc(var(--cosmic-base-spacing) * 0.5);
    margin: 0 0.25rem;
}

.cosmic-nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.cosmic-badge {
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.rounded-cosmic {
    border-radius: var(--cosmic-border-radius) !important;
}

.shadow-cosmic {
    box-shadow: var(--cosmic-shadow) !important;
}

.shadow-cosmic-sm {
    box-shadow: var(--cosmic-shadow-sm) !important;
}

.shadow-cosmic-lg {
    box-shadow: var(--cosmic-shadow-lg) !important;
}

.bg-cosmic-primary {
    background: linear-gradient(135deg, var(--cosmic-primary), #34495e) !important;
}

/* Cosmic Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.cosmic-grid {
    display: grid;
    gap: calc(var(--cosmic-base-spacing) * var(--cosmic-golden-ratio));
}

/* Responsive Design */
@media (max-width: 768px) {
    .cosmic-grid {
        grid-template-columns: 1fr !important;
    }
    
    .cosmic-card {
        margin-bottom: var(--cosmic-base-spacing);
    }
}
`
}
