package enhancement

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
)

// 🚀 PROGRESSIVE ENHANCEMENT SYSTEM
// Sustainable, accessible, future-proof enhancement patterns

type ProgressiveEnhancer struct {
	logger   *zap.Logger
	features map[string]*Feature
	config   *Config
}

type Config struct {
	EnableJS       bool          `json:"enable_js"`
	EnableCSS      bool          `json:"enable_css"`
	EnableWebP     bool          `json:"enable_webp"`
	EnableHTTP2    bool          `json:"enable_http2"`
	CacheTimeout   time.Duration `json:"cache_timeout"`
	MinifyAssets   bool          `json:"minify_assets"`
	LazyLoading    bool          `json:"lazy_loading"`
	ServiceWorker  bool          `json:"service_worker"`
	OfflineSupport bool          `json:"offline_support"`
}

type Feature struct {
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	Required     bool              `json:"required"`
	Fallback     string            `json:"fallback"`
	Dependencies []string          `json:"dependencies"`
	Assets       []Asset           `json:"assets"`
	Conditions   []Condition       `json:"conditions"`
	Metadata     map[string]string `json:"metadata"`
}

type Asset struct {
	Type     string `json:"type"`     // css, js, image, font
	URL      string `json:"url"`
	Integrity string `json:"integrity,omitempty"`
	Async    bool   `json:"async"`
	Defer    bool   `json:"defer"`
	Critical bool   `json:"critical"`
	Media    string `json:"media,omitempty"`
}

type Condition struct {
	Type     string      `json:"type"`     // browser, device, connection, feature
	Property string      `json:"property"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, contains
	Value    interface{} `json:"value"`
}

type ClientCapabilities struct {
	JavaScript    bool   `json:"javascript"`
	CSS3          bool   `json:"css3"`
	WebP          bool   `json:"webp"`
	ServiceWorker bool   `json:"service_worker"`
	LocalStorage  bool   `json:"local_storage"`
	WebSockets    bool   `json:"websockets"`
	TouchEvents   bool   `json:"touch_events"`
	DeviceType    string `json:"device_type"` // mobile, tablet, desktop
	Connection    string `json:"connection"`  // slow, fast, offline
	Viewport      Viewport `json:"viewport"`
	UserAgent     string `json:"user_agent"`
	Language      string `json:"language"`
}

type Viewport struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

type EnhancementResponse struct {
	BaseHTML     string            `json:"base_html"`
	Enhancements []Enhancement     `json:"enhancements"`
	Assets       []Asset           `json:"assets"`
	Config       map[string]string `json:"config"`
	Fallbacks    []string          `json:"fallbacks"`
	Metadata     ResponseMetadata  `json:"metadata"`
}

type Enhancement struct {
	Type        string            `json:"type"`        // css, js, html, meta
	Content     string            `json:"content"`
	Target      string            `json:"target"`      // head, body, element_id
	Position    string            `json:"position"`    // before, after, replace, append
	Conditions  []Condition       `json:"conditions"`
	Priority    int               `json:"priority"`
	Attributes  map[string]string `json:"attributes"`
}

type ResponseMetadata struct {
	GeneratedAt   time.Time `json:"generated_at"`
	CacheKey      string    `json:"cache_key"`
	TTL           int       `json:"ttl"`
	Capabilities  ClientCapabilities `json:"capabilities"`
	FeatureCount  int       `json:"feature_count"`
	AssetCount    int       `json:"asset_count"`
	ResponseTime  string    `json:"response_time"`
}

// NewProgressiveEnhancer creates a new progressive enhancer
func NewProgressiveEnhancer(logger *zap.Logger) *ProgressiveEnhancer {
	return &ProgressiveEnhancer{
		logger:   logger,
		features: make(map[string]*Feature),
		config:   defaultConfig(),
	}
}

func defaultConfig() *Config {
	return &Config{
		EnableJS:       true,
		EnableCSS:      true,
		EnableWebP:     true,
		EnableHTTP2:    true,
		CacheTimeout:   5 * time.Minute,
		MinifyAssets:   true,
		LazyLoading:    true,
		ServiceWorker:  true,
		OfflineSupport: true,
	}
}

// RegisterFeature registers a new progressive enhancement feature
func (pe *ProgressiveEnhancer) RegisterFeature(feature *Feature) {
	pe.features[feature.Name] = feature
	pe.logger.Info("Feature registered", 
		zap.String("name", feature.Name),
		zap.Bool("required", feature.Required),
		zap.Int("assets", len(feature.Assets)),
	)
}

// DetectCapabilities detects client capabilities from request
func (pe *ProgressiveEnhancer) DetectCapabilities(r *http.Request) ClientCapabilities {
	userAgent := r.Header.Get("User-Agent")
	acceptHeader := r.Header.Get("Accept")
	
	caps := ClientCapabilities{
		JavaScript:    true, // Assume JS is available
		CSS3:          true, // Assume modern CSS
		WebP:          strings.Contains(acceptHeader, "image/webp"),
		ServiceWorker: true, // Detect via JS
		LocalStorage:  true, // Detect via JS
		WebSockets:    true, // Detect via JS
		TouchEvents:   isMobileDevice(userAgent),
		DeviceType:    detectDeviceType(userAgent),
		Connection:    detectConnectionSpeed(r),
		UserAgent:     userAgent,
		Language:      detectLanguage(r),
	}

	// Detect viewport from headers or cookies
	if viewport := r.Header.Get("X-Viewport"); viewport != "" {
		// Parse viewport dimensions
		caps.Viewport = parseViewport(viewport)
	}

	return caps
}

// GenerateEnhancements generates progressive enhancements based on capabilities
func (pe *ProgressiveEnhancer) GenerateEnhancements(caps ClientCapabilities) *EnhancementResponse {
	startTime := time.Now()
	
	response := &EnhancementResponse{
		BaseHTML:     pe.generateBaseHTML(),
		Enhancements: []Enhancement{},
		Assets:       []Asset{},
		Config:       make(map[string]string),
		Fallbacks:    []string{},
		Metadata: ResponseMetadata{
			GeneratedAt:  time.Now(),
			Capabilities: caps,
		},
	}

	// Process each feature
	for _, feature := range pe.features {
		if pe.shouldIncludeFeature(feature, caps) {
			enhancements := pe.processFeature(feature, caps)
			response.Enhancements = append(response.Enhancements, enhancements...)
			response.Assets = append(response.Assets, feature.Assets...)
		} else if feature.Fallback != "" {
			response.Fallbacks = append(response.Fallbacks, feature.Fallback)
		}
	}

	// Add core enhancements
	response.Enhancements = append(response.Enhancements, pe.getCoreEnhancements(caps)...)

	// Set metadata
	response.Metadata.FeatureCount = len(pe.features)
	response.Metadata.AssetCount = len(response.Assets)
	response.Metadata.ResponseTime = time.Since(startTime).String()
	response.Metadata.CacheKey = pe.generateCacheKey(caps)
	response.Metadata.TTL = int(pe.config.CacheTimeout.Seconds())

	return response
}

// shouldIncludeFeature determines if a feature should be included
func (pe *ProgressiveEnhancer) shouldIncludeFeature(feature *Feature, caps ClientCapabilities) bool {
	// Check if feature is required
	if feature.Required {
		return true
	}

	// Check conditions
	for _, condition := range feature.Conditions {
		if !pe.evaluateCondition(condition, caps) {
			return false
		}
	}

	return true
}

// evaluateCondition evaluates a single condition
func (pe *ProgressiveEnhancer) evaluateCondition(condition Condition, caps ClientCapabilities) bool {
	var actualValue interface{}

	switch condition.Type {
	case "browser":
		switch condition.Property {
		case "javascript":
			actualValue = caps.JavaScript
		case "css3":
			actualValue = caps.CSS3
		case "webp":
			actualValue = caps.WebP
		case "service_worker":
			actualValue = caps.ServiceWorker
		}
	case "device":
		switch condition.Property {
		case "type":
			actualValue = caps.DeviceType
		case "touch":
			actualValue = caps.TouchEvents
		case "width":
			actualValue = caps.Viewport.Width
		case "height":
			actualValue = caps.Viewport.Height
		}
	case "connection":
		actualValue = caps.Connection
	}

	return pe.compareValues(actualValue, condition.Operator, condition.Value)
}

// compareValues compares two values using the given operator
func (pe *ProgressiveEnhancer) compareValues(actual interface{}, operator string, expected interface{}) bool {
	switch operator {
	case "eq":
		return actual == expected
	case "ne":
		return actual != expected
	case "gt":
		if a, ok := actual.(int); ok {
			if e, ok := expected.(int); ok {
				return a > e
			}
		}
	case "lt":
		if a, ok := actual.(int); ok {
			if e, ok := expected.(int); ok {
				return a < e
			}
		}
	case "contains":
		if a, ok := actual.(string); ok {
			if e, ok := expected.(string); ok {
				return strings.Contains(a, e)
			}
		}
	}
	return false
}

// processFeature processes a feature and returns enhancements
func (pe *ProgressiveEnhancer) processFeature(feature *Feature, caps ClientCapabilities) []Enhancement {
	var enhancements []Enhancement

	// Add CSS enhancements
	if pe.config.EnableCSS {
		for _, asset := range feature.Assets {
			if asset.Type == "css" {
				enhancement := Enhancement{
					Type:     "css",
					Content:  fmt.Sprintf(`<link rel="stylesheet" href="%s">`, asset.URL),
					Target:   "head",
					Position: "append",
					Priority: 1,
				}
				if asset.Media != "" {
					enhancement.Attributes = map[string]string{"media": asset.Media}
				}
				enhancements = append(enhancements, enhancement)
			}
		}
	}

	// Add JavaScript enhancements
	if pe.config.EnableJS && caps.JavaScript {
		for _, asset := range feature.Assets {
			if asset.Type == "js" {
				attrs := ""
				if asset.Async {
					attrs += " async"
				}
				if asset.Defer {
					attrs += " defer"
				}
				
				enhancement := Enhancement{
					Type:     "js",
					Content:  fmt.Sprintf(`<script src="%s"%s></script>`, asset.URL, attrs),
					Target:   "body",
					Position: "append",
					Priority: 2,
				}
				enhancements = append(enhancements, enhancement)
			}
		}
	}

	return enhancements
}

// getCoreEnhancements returns core progressive enhancements
func (pe *ProgressiveEnhancer) getCoreEnhancements(caps ClientCapabilities) []Enhancement {
	var enhancements []Enhancement

	// Add viewport meta tag for mobile
	if caps.DeviceType == "mobile" || caps.TouchEvents {
		enhancements = append(enhancements, Enhancement{
			Type:     "meta",
			Content:  `<meta name="viewport" content="width=device-width, initial-scale=1.0">`,
			Target:   "head",
			Position: "append",
			Priority: 0,
		})
	}

	// Add service worker registration
	if pe.config.ServiceWorker && caps.ServiceWorker {
		enhancements = append(enhancements, Enhancement{
			Type: "js",
			Content: `<script>
				if ('serviceWorker' in navigator) {
					navigator.serviceWorker.register('/sw.js');
				}
			</script>`,
			Target:   "body",
			Position: "append",
			Priority: 3,
		})
	}

	// Add lazy loading for images
	if pe.config.LazyLoading {
		enhancements = append(enhancements, Enhancement{
			Type: "js",
			Content: `<script>
				document.addEventListener('DOMContentLoaded', function() {
					const images = document.querySelectorAll('img[data-src]');
					const imageObserver = new IntersectionObserver((entries, observer) => {
						entries.forEach(entry => {
							if (entry.isIntersecting) {
								const img = entry.target;
								img.src = img.dataset.src;
								img.classList.remove('lazy');
								imageObserver.unobserve(img);
							}
						});
					});
					images.forEach(img => imageObserver.observe(img));
				});
			</script>`,
			Target:   "body",
			Position: "append",
			Priority: 4,
		})
	}

	return enhancements
}

// generateBaseHTML generates the base HTML structure
func (pe *ProgressiveEnhancer) generateBaseHTML() string {
	return `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>HVAC CRM System</title>
    <meta name="description" content="Professional HVAC CRM System">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <noscript>
        <div class="alert alert-warning">
            This application works best with JavaScript enabled.
        </div>
    </noscript>
    <div id="app">
        <!-- Content will be enhanced progressively -->
    </div>
</body>
</html>`
}

// Helper functions
func isMobileDevice(userAgent string) bool {
	mobileKeywords := []string{"Mobile", "Android", "iPhone", "iPad", "Windows Phone"}
	for _, keyword := range mobileKeywords {
		if strings.Contains(userAgent, keyword) {
			return true
		}
	}
	return false
}

func detectDeviceType(userAgent string) string {
	if strings.Contains(userAgent, "Mobile") {
		return "mobile"
	}
	if strings.Contains(userAgent, "Tablet") || strings.Contains(userAgent, "iPad") {
		return "tablet"
	}
	return "desktop"
}

func detectConnectionSpeed(r *http.Request) string {
	// Check for connection hints
	if saveData := r.Header.Get("Save-Data"); saveData == "on" {
		return "slow"
	}
	// Default to fast connection
	return "fast"
}

func detectLanguage(r *http.Request) string {
	acceptLang := r.Header.Get("Accept-Language")
	if acceptLang != "" {
		// Parse first language
		if parts := strings.Split(acceptLang, ","); len(parts) > 0 {
			if lang := strings.Split(parts[0], ";"); len(lang) > 0 {
				return strings.TrimSpace(lang[0])
			}
		}
	}
	return "pl" // Default to Polish
}

func parseViewport(viewport string) Viewport {
	// Simple viewport parsing - in real implementation, use proper parsing
	return Viewport{Width: 1920, Height: 1080}
}

func (pe *ProgressiveEnhancer) generateCacheKey(caps ClientCapabilities) string {
	data, _ := json.Marshal(caps)
	return fmt.Sprintf("pe_%x", data)
}
