package templates

import (
	"bytes"
	"fmt"
	"html/template"
	"io"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 🎨 SUSTAINABLE TEMPLATE ENGINE
// Type-safe, hot-reload capable, cosmic-level template system

type Engine struct {
	templates map[string]*template.Template
	cache     *sync.Map
	logger    *zap.Logger
	config    *Config
	funcs     template.FuncMap
	mu        sync.RWMutex
}

type Config struct {
	TemplateDir   string
	HotReload     bool
	CacheEnabled  bool
	CacheTTL      time.Duration
	Development   bool
	GoldenRatio   float64 // φ = 1.618
}

type TemplateData struct {
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Keywords    []string               `json:"keywords"`
	Data        interface{}            `json:"data"`
	Meta        map[string]interface{} `json:"meta"`
	UI          UIContext              `json:"ui"`
	User        *UserContext           `json:"user,omitempty"`
	System      SystemContext          `json:"system"`
	Timestamp   time.Time              `json:"timestamp"`
}

type UIContext struct {
	Theme         string                 `json:"theme"`
	Language      string                 `json:"language"`
	Direction     string                 `json:"direction"` // ltr, rtl
	Viewport      ViewportInfo           `json:"viewport"`
	Navigation    []NavigationItem       `json:"navigation"`
	Breadcrumbs   []BreadcrumbItem       `json:"breadcrumbs"`
	Actions       []ActionButton         `json:"actions"`
	Notifications []Notification         `json:"notifications"`
	Widgets       []Widget               `json:"widgets"`
	Layout        LayoutConfig           `json:"layout"`
}

type ViewportInfo struct {
	Width      int    `json:"width"`
	Height     int    `json:"height"`
	DeviceType string `json:"device_type"` // mobile, tablet, desktop
	IsMobile   bool   `json:"is_mobile"`
	IsTablet   bool   `json:"is_tablet"`
	IsDesktop  bool   `json:"is_desktop"`
}

type NavigationItem struct {
	ID       string           `json:"id"`
	Label    string           `json:"label"`
	Icon     string           `json:"icon"`
	URL      string           `json:"url"`
	Active   bool             `json:"active"`
	Badge    string           `json:"badge,omitempty"`
	Children []NavigationItem `json:"children,omitempty"`
}

type BreadcrumbItem struct {
	Label string `json:"label"`
	URL   string `json:"url,omitempty"`
	Icon  string `json:"icon,omitempty"`
}

type ActionButton struct {
	ID           string                 `json:"id"`
	Label        string                 `json:"label"`
	Icon         string                 `json:"icon"`
	Color        string                 `json:"color"`
	Size         string                 `json:"size"`
	URL          string                 `json:"url,omitempty"`
	Action       string                 `json:"action,omitempty"`
	Confirmation bool                   `json:"confirmation"`
	Params       map[string]interface{} `json:"params,omitempty"`
}

type Notification struct {
	ID       string    `json:"id"`
	Type     string    `json:"type"` // success, warning, error, info
	Title    string    `json:"title"`
	Message  string    `json:"message"`
	Icon     string    `json:"icon"`
	Duration int       `json:"duration"` // seconds, 0 = persistent
	Actions  []string  `json:"actions,omitempty"`
	Created  time.Time `json:"created"`
}

type Widget struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title"`
	Data     interface{}            `json:"data"`
	Config   map[string]interface{} `json:"config"`
	Position WidgetPosition         `json:"position"`
}

type WidgetPosition struct {
	Row    int `json:"row"`
	Col    int `json:"col"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

type LayoutConfig struct {
	Sidebar      SidebarConfig `json:"sidebar"`
	Header       HeaderConfig  `json:"header"`
	Footer       FooterConfig  `json:"footer"`
	Container    string        `json:"container"` // fluid, fixed
	GoldenRatio  bool          `json:"golden_ratio"`
	CosmicSpacing bool         `json:"cosmic_spacing"`
}

type SidebarConfig struct {
	Enabled   bool   `json:"enabled"`
	Collapsed bool   `json:"collapsed"`
	Width     string `json:"width"`
	Position  string `json:"position"` // left, right
}

type HeaderConfig struct {
	Enabled bool   `json:"enabled"`
	Fixed   bool   `json:"fixed"`
	Height  string `json:"height"`
}

type FooterConfig struct {
	Enabled bool   `json:"enabled"`
	Fixed   bool   `json:"fixed"`
	Height  string `json:"height"`
}

type UserContext struct {
	ID          int64    `json:"id"`
	Name        string   `json:"name"`
	Email       string   `json:"email"`
	Avatar      string   `json:"avatar,omitempty"`
	Role        string   `json:"role"`
	Permissions []string `json:"permissions"`
	Preferences UserPreferences `json:"preferences"`
}

type UserPreferences struct {
	Theme      string `json:"theme"`
	Language   string `json:"language"`
	Timezone   string `json:"timezone"`
	DateFormat string `json:"date_format"`
	TimeFormat string `json:"time_format"`
}

type SystemContext struct {
	Version     string    `json:"version"`
	Environment string    `json:"environment"`
	Debug       bool      `json:"debug"`
	Uptime      string    `json:"uptime"`
	LoadTime    string    `json:"load_time"`
	RequestID   string    `json:"request_id"`
	Timestamp   time.Time `json:"timestamp"`
}

// NewEngine creates a new template engine
func NewEngine(config *Config, logger *zap.Logger) *Engine {
	if config == nil {
		config = DefaultConfig()
	}

	engine := &Engine{
		templates: make(map[string]*template.Template),
		cache:     &sync.Map{},
		logger:    logger,
		config:    config,
		funcs:     defaultFuncMap(),
	}

	return engine
}

// DefaultConfig returns default configuration
func DefaultConfig() *Config {
	return &Config{
		TemplateDir:   "./templates",
		HotReload:     true,
		CacheEnabled:  true,
		CacheTTL:      5 * time.Minute,
		Development:   true,
		GoldenRatio:   1.618,
	}
}

// defaultFuncMap returns default template functions
func defaultFuncMap() template.FuncMap {
	return template.FuncMap{
		"formatTime": func(t time.Time) string {
			return t.Format("2006-01-02 15:04:05")
		},
		"formatDate": func(t time.Time) string {
			return t.Format("2006-01-02")
		},
		"formatCurrency": func(amount float64) string {
			return fmt.Sprintf("%.2f PLN", amount)
		},
		"formatPercent": func(value float64) string {
			return fmt.Sprintf("%.1f%%", value*100)
		},
		"goldenRatio": func() float64 {
			return 1.618
		},
		"cosmicSpacing": func(base float64) float64 {
			return base * 1.618
		},
		"humanizeTime": func(t time.Time) string {
			duration := time.Since(t)
			if duration < time.Minute {
				return "just now"
			} else if duration < time.Hour {
				return fmt.Sprintf("%d minutes ago", int(duration.Minutes()))
			} else if duration < 24*time.Hour {
				return fmt.Sprintf("%d hours ago", int(duration.Hours()))
			} else {
				return fmt.Sprintf("%d days ago", int(duration.Hours()/24))
			}
		},
		"statusColor": func(status string) string {
			colors := map[string]string{
				"active":    "success",
				"inactive":  "secondary",
				"pending":   "warning",
				"completed": "success",
				"failed":    "danger",
				"cancelled": "secondary",
			}
			if color, ok := colors[status]; ok {
				return color
			}
			return "primary"
		},
		"priorityIcon": func(priority string) string {
			icons := map[string]string{
				"low":      "fas fa-arrow-down text-success",
				"normal":   "fas fa-minus text-info",
				"high":     "fas fa-arrow-up text-warning",
				"urgent":   "fas fa-exclamation text-danger",
				"critical": "fas fa-exclamation-triangle text-danger",
			}
			if icon, ok := icons[priority]; ok {
				return icon
			}
			return "fas fa-circle text-secondary"
		},
	}
}

// LoadTemplates loads all templates from the template directory
func (e *Engine) LoadTemplates() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	pattern := filepath.Join(e.config.TemplateDir, "*.html")
	templates, err := template.New("").Funcs(e.funcs).ParseGlob(pattern)
	if err != nil {
		return fmt.Errorf("failed to parse templates: %w", err)
	}

	// Store templates by name
	for _, tmpl := range templates.Templates() {
		e.templates[tmpl.Name()] = tmpl
	}

	e.logger.Info("Templates loaded successfully", 
		zap.Int("count", len(e.templates)),
		zap.String("pattern", pattern),
	)

	return nil
}

// Render renders a template with the given data
func (e *Engine) Render(w io.Writer, name string, data *TemplateData) error {
	// Add system context
	if data.System.Timestamp.IsZero() {
		data.System.Timestamp = time.Now()
		data.System.RequestID = generateRequestID()
	}

	// Check cache first
	if e.config.CacheEnabled {
		if cached, ok := e.getCached(name, data); ok {
			_, err := w.Write(cached)
			return err
		}
	}

	// Get template
	tmpl, err := e.getTemplate(name)
	if err != nil {
		return err
	}

	// Render template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return fmt.Errorf("failed to execute template %s: %w", name, err)
	}

	// Cache result
	if e.config.CacheEnabled {
		e.setCached(name, data, buf.Bytes())
	}

	// Write to output
	_, err = w.Write(buf.Bytes())
	return err
}

// getTemplate gets a template by name
func (e *Engine) getTemplate(name string) (*template.Template, error) {
	e.mu.RLock()
	tmpl, exists := e.templates[name]
	e.mu.RUnlock()

	if !exists {
		if e.config.HotReload {
			// Try to reload templates
			if err := e.LoadTemplates(); err != nil {
				return nil, fmt.Errorf("template %s not found and reload failed: %w", name, err)
			}
			
			e.mu.RLock()
			tmpl, exists = e.templates[name]
			e.mu.RUnlock()
		}
		
		if !exists {
			return nil, fmt.Errorf("template %s not found", name)
		}
	}

	return tmpl, nil
}

// getCached retrieves cached template result
func (e *Engine) getCached(name string, data *TemplateData) ([]byte, bool) {
	key := fmt.Sprintf("%s:%s", name, generateCacheKey(data))
	if cached, ok := e.cache.Load(key); ok {
		if entry, ok := cached.(*cacheEntry); ok {
			if time.Since(entry.created) < e.config.CacheTTL {
				return entry.data, true
			}
			e.cache.Delete(key)
		}
	}
	return nil, false
}

// setCached stores template result in cache
func (e *Engine) setCached(name string, data *TemplateData, result []byte) {
	key := fmt.Sprintf("%s:%s", name, generateCacheKey(data))
	entry := &cacheEntry{
		data:    result,
		created: time.Now(),
	}
	e.cache.Store(key, entry)
}

type cacheEntry struct {
	data    []byte
	created time.Time
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// generateCacheKey generates a cache key from template data
func generateCacheKey(data *TemplateData) string {
	// Simple hash based on title and timestamp
	return fmt.Sprintf("%s_%d", data.Title, data.Timestamp.Unix())
}
