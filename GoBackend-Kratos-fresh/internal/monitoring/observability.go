package monitoring

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// 📊 COMPREHENSIVE MONITORING & OBSERVABILITY
// Sustainable, performance-focused monitoring system

type ObservabilityStack struct {
	logger     *zap.Logger
	metrics    *MetricsCollector
	tracer     *Tracer
	profiler   *Profiler
	alerting   *AlertManager
	config     *Config
	mu         sync.RWMutex
}

type Config struct {
	ServiceName     string        `json:"service_name"`
	Environment     string        `json:"environment"`
	Version         string        `json:"version"`
	MetricsInterval time.Duration `json:"metrics_interval"`
	TraceEnabled    bool          `json:"trace_enabled"`
	ProfileEnabled  bool          `json:"profile_enabled"`
	AlertsEnabled   bool          `json:"alerts_enabled"`
	LogLevel        string        `json:"log_level"`
	SamplingRate    float64       `json:"sampling_rate"`
}

type MetricsCollector struct {
	counters   map[string]*Counter
	gauges     map[string]*Gauge
	histograms map[string]*Histogram
	timers     map[string]*Timer
	mu         sync.RWMutex
	startTime  time.Time
}

type Counter struct {
	Name        string            `json:"name"`
	Value       int64             `json:"value"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	mu          sync.RWMutex
}

type Gauge struct {
	Name        string            `json:"name"`
	Value       float64           `json:"value"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	mu          sync.RWMutex
}

type Histogram struct {
	Name        string            `json:"name"`
	Buckets     []float64         `json:"buckets"`
	Counts      []int64           `json:"counts"`
	Sum         float64           `json:"sum"`
	Count       int64             `json:"count"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	mu          sync.RWMutex
}

type Timer struct {
	Name        string            `json:"name"`
	StartTime   time.Time         `json:"start_time"`
	Duration    time.Duration     `json:"duration"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	Active      bool              `json:"active"`
	mu          sync.RWMutex
}

type Tracer struct {
	spans   map[string]*Span
	enabled bool
	mu      sync.RWMutex
}

type Span struct {
	TraceID     string            `json:"trace_id"`
	SpanID      string            `json:"span_id"`
	ParentID    string            `json:"parent_id,omitempty"`
	Operation   string            `json:"operation"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     *time.Time        `json:"end_time,omitempty"`
	Duration    time.Duration     `json:"duration"`
	Tags        map[string]string `json:"tags"`
	Logs        []SpanLog         `json:"logs"`
	Status      SpanStatus        `json:"status"`
	mu          sync.RWMutex
}

type SpanLog struct {
	Timestamp time.Time         `json:"timestamp"`
	Level     string            `json:"level"`
	Message   string            `json:"message"`
	Fields    map[string]string `json:"fields"`
}

type SpanStatus int

const (
	SpanStatusOK SpanStatus = iota
	SpanStatusError
	SpanStatusTimeout
	SpanStatusCancelled
)

type Profiler struct {
	enabled   bool
	profiles  map[string]*Profile
	mu        sync.RWMutex
}

type Profile struct {
	Name      string    `json:"name"`
	Type      string    `json:"type"` // cpu, memory, goroutine, heap
	StartTime time.Time `json:"start_time"`
	Duration  time.Duration `json:"duration"`
	Data      []byte    `json:"data"`
	Size      int64     `json:"size"`
}

type AlertManager struct {
	rules     map[string]*AlertRule
	alerts    map[string]*Alert
	channels  map[string]AlertChannel
	enabled   bool
	mu        sync.RWMutex
}

type AlertRule struct {
	Name        string            `json:"name"`
	Condition   string            `json:"condition"`
	Threshold   float64           `json:"threshold"`
	Duration    time.Duration     `json:"duration"`
	Severity    AlertSeverity     `json:"severity"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Enabled     bool              `json:"enabled"`
}

type Alert struct {
	ID          string            `json:"id"`
	RuleName    string            `json:"rule_name"`
	Status      AlertStatus       `json:"status"`
	Severity    AlertSeverity     `json:"severity"`
	Message     string            `json:"message"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	StartsAt    time.Time         `json:"starts_at"`
	EndsAt      *time.Time        `json:"ends_at,omitempty"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

type AlertStatus int

const (
	AlertStatusFiring AlertStatus = iota
	AlertStatusResolved
	AlertStatusSuppressed
)

type AlertSeverity int

const (
	AlertSeverityInfo AlertSeverity = iota
	AlertSeverityWarning
	AlertSeverityError
	AlertSeverityCritical
)

type AlertChannel interface {
	Send(ctx context.Context, alert *Alert) error
	Name() string
}

// NewObservabilityStack creates a new observability stack
func NewObservabilityStack(config *Config) (*ObservabilityStack, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Create logger
	logger, err := createLogger(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create logger: %w", err)
	}

	// Create metrics collector
	metrics := &MetricsCollector{
		counters:   make(map[string]*Counter),
		gauges:     make(map[string]*Gauge),
		histograms: make(map[string]*Histogram),
		timers:     make(map[string]*Timer),
		startTime:  time.Now(),
	}

	// Create tracer
	tracer := &Tracer{
		spans:   make(map[string]*Span),
		enabled: config.TraceEnabled,
	}

	// Create profiler
	profiler := &Profiler{
		enabled:  config.ProfileEnabled,
		profiles: make(map[string]*Profile),
	}

	// Create alert manager
	alertManager := &AlertManager{
		rules:    make(map[string]*AlertRule),
		alerts:   make(map[string]*Alert),
		channels: make(map[string]AlertChannel),
		enabled:  config.AlertsEnabled,
	}

	stack := &ObservabilityStack{
		logger:   logger,
		metrics:  metrics,
		tracer:   tracer,
		profiler: profiler,
		alerting: alertManager,
		config:   config,
	}

	// Start background processes
	go stack.startMetricsCollection()
	go stack.startAlertEvaluation()

	logger.Info("Observability stack initialized",
		zap.String("service", config.ServiceName),
		zap.String("environment", config.Environment),
		zap.String("version", config.Version),
	)

	return stack, nil
}

func DefaultConfig() *Config {
	return &Config{
		ServiceName:     "hvac-crm",
		Environment:     "development",
		Version:         "1.0.0",
		MetricsInterval: 30 * time.Second,
		TraceEnabled:    true,
		ProfileEnabled:  true,
		AlertsEnabled:   true,
		LogLevel:        "info",
		SamplingRate:    1.0,
	}
}

func createLogger(config *Config) (*zap.Logger, error) {
	var level zapcore.Level
	switch config.LogLevel {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	zapConfig := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: config.Environment == "development",
		Sampling: &zap.SamplingConfig{
			Initial:    100,
			Thereafter: 100,
		},
		Encoding: "json",
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "timestamp",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			FunctionKey:    zapcore.OmitKey,
			MessageKey:     "message",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
		InitialFields: map[string]interface{}{
			"service":     config.ServiceName,
			"environment": config.Environment,
			"version":     config.Version,
		},
	}

	return zapConfig.Build()
}

// Metrics methods
func (o *ObservabilityStack) Counter(name string, labels map[string]string) *Counter {
	o.metrics.mu.Lock()
	defer o.metrics.mu.Unlock()

	key := fmt.Sprintf("%s_%s", name, labelsToString(labels))
	if counter, exists := o.metrics.counters[key]; exists {
		return counter
	}

	counter := &Counter{
		Name:   name,
		Value:  0,
		Labels: labels,
	}
	o.metrics.counters[key] = counter
	return counter
}

func (c *Counter) Inc() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.Value++
}

func (c *Counter) Add(value int64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.Value += value
}

func (o *ObservabilityStack) Gauge(name string, labels map[string]string) *Gauge {
	o.metrics.mu.Lock()
	defer o.metrics.mu.Unlock()

	key := fmt.Sprintf("%s_%s", name, labelsToString(labels))
	if gauge, exists := o.metrics.gauges[key]; exists {
		return gauge
	}

	gauge := &Gauge{
		Name:   name,
		Value:  0,
		Labels: labels,
	}
	o.metrics.gauges[key] = gauge
	return gauge
}

func (g *Gauge) Set(value float64) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.Value = value
}

func (g *Gauge) Inc() {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.Value++
}

func (g *Gauge) Dec() {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.Value--
}

// Tracing methods
func (o *ObservabilityStack) StartSpan(ctx context.Context, operation string) (*Span, context.Context) {
	if !o.tracer.enabled {
		return nil, ctx
	}

	span := &Span{
		TraceID:   generateTraceID(),
		SpanID:    generateSpanID(),
		Operation: operation,
		StartTime: time.Now(),
		Tags:      make(map[string]string),
		Logs:      []SpanLog{},
		Status:    SpanStatusOK,
	}

	// Check for parent span in context
	if parentSpan := SpanFromContext(ctx); parentSpan != nil {
		span.TraceID = parentSpan.TraceID
		span.ParentID = parentSpan.SpanID
	}

	o.tracer.mu.Lock()
	o.tracer.spans[span.SpanID] = span
	o.tracer.mu.Unlock()

	// Add span to context
	ctx = ContextWithSpan(ctx, span)

	return span, ctx
}

func (s *Span) Finish() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	now := time.Now()
	s.EndTime = &now
	s.Duration = now.Sub(s.StartTime)
}

func (s *Span) SetTag(key, value string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.Tags[key] = value
}

func (s *Span) LogFields(level, message string, fields map[string]string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	log := SpanLog{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}
	s.Logs = append(s.Logs, log)
}

// Background processes
func (o *ObservabilityStack) startMetricsCollection() {
	ticker := time.NewTicker(o.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		o.collectSystemMetrics()
	}
}

func (o *ObservabilityStack) collectSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Memory metrics
	o.Gauge("memory_alloc_bytes", nil).Set(float64(m.Alloc))
	o.Gauge("memory_total_alloc_bytes", nil).Set(float64(m.TotalAlloc))
	o.Gauge("memory_sys_bytes", nil).Set(float64(m.Sys))
	o.Gauge("memory_heap_alloc_bytes", nil).Set(float64(m.HeapAlloc))
	o.Gauge("memory_heap_sys_bytes", nil).Set(float64(m.HeapSys))

	// Goroutine metrics
	o.Gauge("goroutines_count", nil).Set(float64(runtime.NumGoroutine()))

	// GC metrics
	o.Counter("gc_runs_total", nil).Add(int64(m.NumGC))
	o.Gauge("gc_pause_ns", nil).Set(float64(m.PauseNs[(m.NumGC+255)%256]))

	// CPU metrics
	o.Gauge("cpu_cores", nil).Set(float64(runtime.NumCPU()))
}

func (o *ObservabilityStack) startAlertEvaluation() {
	if !o.alerting.enabled {
		return
	}

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		o.evaluateAlerts()
	}
}

func (o *ObservabilityStack) evaluateAlerts() {
	// Implement alert evaluation logic
	// This would check metrics against alert rules
}

// Helper functions
func labelsToString(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}
	// Simple string representation of labels
	return fmt.Sprintf("%v", labels)
}

func generateTraceID() string {
	return fmt.Sprintf("trace_%d", time.Now().UnixNano())
}

func generateSpanID() string {
	return fmt.Sprintf("span_%d", time.Now().UnixNano())
}

// Context helpers
type spanKey struct{}

func ContextWithSpan(ctx context.Context, span *Span) context.Context {
	return context.WithValue(ctx, spanKey{}, span)
}

func SpanFromContext(ctx context.Context) *Span {
	if span, ok := ctx.Value(spanKey{}).(*Span); ok {
		return span
	}
	return nil
}
