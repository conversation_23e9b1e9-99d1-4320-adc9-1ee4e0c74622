package hvaccrm

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 🌀 FIBONACCI ERA 13 - HVAC CRM ERP SYSTEM CASCADE
// 13 HVAC-specific CRM/ERP tasks building on sustainable Go foundation

type HVACCRMSystem struct {
	// Task 1: HVAC Customer Lifecycle Management
	customerLifecycle *CustomerLifecycleManager

	// Task 2: Equipment Registry & Maintenance Tracking
	equipmentRegistry *EquipmentRegistrySystem

	// Task 3: Service Scheduling & Dispatch System
	serviceScheduler *ServiceSchedulingSystem

	// Task 4: HVAC Inventory & Parts Management
	inventoryManager *HVACInventoryManager

	// Task 5: Technician Management & Skills Tracking
	technicianManager *TechnicianManagementSystem

	// Task 6: HVAC Quote & Proposal Generator
	quoteGenerator *HVACQuoteGenerator

	// Task 7: Energy Efficiency Analytics
	energyAnalytics *EnergyEfficiencyAnalytics

	// Task 8: HVAC Compliance & Certification Tracking
	complianceTracker *HVACComplianceTracker

	// Task 9: Customer Portal & Mobile App Backend
	customerPortal *CustomerPortalSystem

	// Task 10: HVAC Business Intelligence Dashboard
	businessIntelligence *HVACBusinessIntelligence

	// Task 11: Integration Hub (IoT, Smart Thermostats)
	integrationHub *HVACIntegrationHub

	// Task 12: Financial Management (Invoicing, Payments)
	financialManager *HVACFinancialManager

	// Task 13: HVAC Knowledge Base & AI Assistant
	knowledgeBase *HVACKnowledgeBase

	config *HVACCRMConfig
	logger *zap.Logger
	cache  *HVACRedisCache
	mu     sync.RWMutex
}

type HVACCRMConfig struct {
	CompanyName        string `json:"company_name"`
	ServiceArea        string `json:"service_area"`
	BusinessType       string `json:"business_type"` // residential, commercial, industrial
	SeasonalOperations bool   `json:"seasonal_operations"`
	EmergencyServices  bool   `json:"emergency_services"`
	WarrantyPeriod     int    `json:"warranty_period_months"`
	ServiceRadius      int    `json:"service_radius_km"`
}

// Task 1: HVAC Customer Lifecycle Management
type CustomerLifecycleManager struct {
	stages map[CustomerStage]*LifecycleStage
	rules  map[string]*LifecycleRule
	mu     sync.RWMutex
}

type CustomerStage int

const (
	StageLead CustomerStage = iota
	StageProspect
	StageQuoteRequested
	StageQuoteProvided
	StageJobScheduled
	StageServiceInProgress
	StageJobCompleted
	StageMaintenanceCustomer
	StageVIPCustomer
	StageInactiveCustomer
	StageChurnedCustomer
)

type LifecycleStage struct {
	Name            string             `json:"name"`
	Description     string             `json:"description"`
	AverageStayDays int                `json:"average_stay_days"`
	NextStages      []CustomerStage    `json:"next_stages"`
	Actions         []StageAction      `json:"actions"`
	Triggers        []StageTrigger     `json:"triggers"`
	KPIs            map[string]float64 `json:"kpis"`
}

type StageAction struct {
	Type        string `json:"type"` // email, call, visit, quote
	Description string `json:"description"`
	DaysAfter   int    `json:"days_after"`
	Priority    string `json:"priority"`
	Automated   bool   `json:"automated"`
}

type StageTrigger struct {
	Event     string `json:"event"` // equipment_failure, season_change, warranty_expiry
	Condition string `json:"condition"`
	Action    string `json:"action"`
}

// Task 2: Equipment Registry & Maintenance Tracking
type EquipmentRegistrySystem struct {
	equipment            map[string]*HVACEquipment
	maintenanceSchedules map[string]*MaintenanceSchedule
	warranties           map[string]*WarrantyInfo
	mu                   sync.RWMutex
}

type HVACEquipment struct {
	ID               string             `json:"id"`
	CustomerID       string             `json:"customer_id"`
	Type             HVACEquipmentType  `json:"type"`
	Brand            string             `json:"brand"`
	Model            string             `json:"model"`
	SerialNumber     string             `json:"serial_number"`
	InstallationDate time.Time          `json:"installation_date"`
	Location         EquipmentLocation  `json:"location"`
	Specifications   HVACSpecifications `json:"specifications"`
	HealthScore      float64            `json:"health_score"`
	LastService      *time.Time         `json:"last_service,omitempty"`
	NextService      *time.Time         `json:"next_service,omitempty"`
	ServiceHistory   []ServiceRecord    `json:"service_history"`
	WarrantyInfo     *WarrantyInfo      `json:"warranty_info,omitempty"`
	IoTSensorID      string             `json:"iot_sensor_id,omitempty"`
	EnergyRating     string             `json:"energy_rating"`
	RefrigerantType  string             `json:"refrigerant_type,omitempty"`
}

type HVACEquipmentType int

const (
	EquipmentCentralAC HVACEquipmentType = iota
	EquipmentHeatPump
	EquipmentFurnace
	EquipmentBoiler
	EquipmentDuctwork
	EquipmentThermostat
	EquipmentAirHandler
	EquipmentCondenser
	EquipmentEvaporator
	EquipmentVentilation
	EquipmentHumidifier
	EquipmentDehumidifier
)

type EquipmentLocation struct {
	Building string          `json:"building"`
	Floor    string          `json:"floor"`
	Room     string          `json:"room"`
	Zone     string          `json:"zone"`
	GPS      *GPSCoordinates `json:"gps,omitempty"`
}

type GPSCoordinates struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

type HVACSpecifications struct {
	Capacity        float64 `json:"capacity_btu"`
	Voltage         int     `json:"voltage"`
	Amperage        float64 `json:"amperage"`
	Efficiency      float64 `json:"efficiency_seer"`
	RefrigerantType string  `json:"refrigerant_type"`
	FilterSize      string  `json:"filter_size"`
	DuctSize        string  `json:"duct_size,omitempty"`
	MaxTemperature  int     `json:"max_temperature"`
	MinTemperature  int     `json:"min_temperature"`
	NoiseLevel      int     `json:"noise_level_db"`
}

type ServiceRecord struct {
	ID             string    `json:"id"`
	Date           time.Time `json:"date"`
	TechnicianID   string    `json:"technician_id"`
	ServiceType    string    `json:"service_type"`
	Description    string    `json:"description"`
	PartsUsed      []Part    `json:"parts_used"`
	LaborHours     float64   `json:"labor_hours"`
	Cost           float64   `json:"cost"`
	WarrantyWork   bool      `json:"warranty_work"`
	CustomerRating int       `json:"customer_rating"`
	Photos         []string  `json:"photos"`
	Notes          string    `json:"notes"`
}

type Part struct {
	PartNumber   string  `json:"part_number"`
	Description  string  `json:"description"`
	Quantity     int     `json:"quantity"`
	UnitCost     float64 `json:"unit_cost"`
	WarrantyDays int     `json:"warranty_days"`
	Supplier     string  `json:"supplier"`
}

type WarrantyInfo struct {
	Type         string    `json:"type"` // manufacturer, extended, labor
	StartDate    time.Time `json:"start_date"`
	EndDate      time.Time `json:"end_date"`
	Coverage     string    `json:"coverage"` // parts, labor, full
	Provider     string    `json:"provider"`
	PolicyNumber string    `json:"policy_number,omitempty"`
	Terms        string    `json:"terms"`
}

// Task 3: Service Scheduling & Dispatch System
type ServiceSchedulingSystem struct {
	appointments map[string]*ServiceAppointment
	technicians  map[string]*Technician
	routes       map[string]*ServiceRoute
	calendar     *ServiceCalendar
	mu           sync.RWMutex
}

type ServiceAppointment struct {
	ID             string                 `json:"id"`
	CustomerID     string                 `json:"customer_id"`
	EquipmentID    string                 `json:"equipment_id,omitempty"`
	Type           ServiceAppointmentType `json:"type"`
	Priority       ServicePriority        `json:"priority"`
	Status         AppointmentStatus      `json:"status"`
	ScheduledStart time.Time              `json:"scheduled_start"`
	ScheduledEnd   time.Time              `json:"scheduled_end"`
	ActualStart    *time.Time             `json:"actual_start,omitempty"`
	ActualEnd      *time.Time             `json:"actual_end,omitempty"`
	TechnicianID   string                 `json:"technician_id"`
	Description    string                 `json:"description"`
	RequiredSkills []string               `json:"required_skills"`
	EstimatedHours float64                `json:"estimated_hours"`
	PartsNeeded    []string               `json:"parts_needed"`
	SpecialTools   []string               `json:"special_tools"`
	CustomerNotes  string                 `json:"customer_notes"`
	InternalNotes  string                 `json:"internal_notes"`
	Address        ServiceAddress         `json:"address"`
	EmergencyCall  bool                   `json:"emergency_call"`
	FollowUpNeeded bool                   `json:"follow_up_needed"`
}

type ServiceAppointmentType int

const (
	AppointmentInstallation ServiceAppointmentType = iota
	AppointmentMaintenance
	AppointmentRepair
	AppointmentInspection
	AppointmentEmergency
	AppointmentConsultation
	AppointmentWarranty
	AppointmentUpgrade
	AppointmentCleaning
	AppointmentDiagnostic
)

type ServicePriority int

const (
	PriorityRoutine ServicePriority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
	PriorityEmergency
)

type AppointmentStatus int

const (
	StatusScheduled AppointmentStatus = iota
	StatusConfirmed
	StatusEnRoute
	StatusInProgress
	StatusCompleted
	StatusCancelled
	StatusRescheduled
	StatusOnHold
)

type Technician struct {
	ID                 string             `json:"id"`
	Name               string             `json:"name"`
	Email              string             `json:"email"`
	Phone              string             `json:"phone"`
	Skills             []TechnicianSkill  `json:"skills"`
	Certifications     []Certification    `json:"certifications"`
	ServiceArea        []string           `json:"service_area"`
	Availability       WeeklyAvailability `json:"availability"`
	CurrentLocation    *GPSCoordinates    `json:"current_location,omitempty"`
	VehicleInfo        *VehicleInfo       `json:"vehicle_info,omitempty"`
	Rating             float64            `json:"rating"`
	CompletedJobs      int                `json:"completed_jobs"`
	Status             TechnicianStatus   `json:"status"`
	HourlyRate         float64            `json:"hourly_rate"`
	EmergencyAvailable bool               `json:"emergency_available"`
}

type TechnicianSkill struct {
	Name      string `json:"name"`
	Level     int    `json:"level"` // 1-5
	Certified bool   `json:"certified"`
	YearsExp  int    `json:"years_exp"`
}

type Certification struct {
	Name       string    `json:"name"`
	Number     string    `json:"number"`
	IssuedBy   string    `json:"issued_by"`
	IssuedDate time.Time `json:"issued_date"`
	ExpiryDate time.Time `json:"expiry_date"`
	Active     bool      `json:"active"`
}

type WeeklyAvailability struct {
	Monday    DayAvailability `json:"monday"`
	Tuesday   DayAvailability `json:"tuesday"`
	Wednesday DayAvailability `json:"wednesday"`
	Thursday  DayAvailability `json:"thursday"`
	Friday    DayAvailability `json:"friday"`
	Saturday  DayAvailability `json:"saturday"`
	Sunday    DayAvailability `json:"sunday"`
}

type DayAvailability struct {
	Available bool      `json:"available"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Breaks    []Break   `json:"breaks"`
}

type Break struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Type      string    `json:"type"` // lunch, break, travel
}

type TechnicianStatus int

const (
	TechStatusAvailable TechnicianStatus = iota
	TechStatusBusy
	TechStatusEnRoute
	TechStatusOffDuty
	TechStatusOnCall
	TechStatusVacation
	TechStatusSick
)

type VehicleInfo struct {
	Make         string   `json:"make"`
	Model        string   `json:"model"`
	Year         int      `json:"year"`
	LicensePlate string   `json:"license_plate"`
	Capacity     string   `json:"capacity"`
	Equipment    []string `json:"equipment"`
	FuelType     string   `json:"fuel_type"`
	GPS          bool     `json:"gps_enabled"`
}

type ServiceRoute struct {
	ID            string          `json:"id"`
	TechnicianID  string          `json:"technician_id"`
	Date          time.Time       `json:"date"`
	Appointments  []string        `json:"appointment_ids"`
	StartLocation *GPSCoordinates `json:"start_location"`
	EndLocation   *GPSCoordinates `json:"end_location"`
	TotalDistance float64         `json:"total_distance_km"`
	EstimatedTime time.Duration   `json:"estimated_time"`
	Status        RouteStatus     `json:"status"`
	Optimized     bool            `json:"optimized"`
}

type RouteStatus int

const (
	RouteStatusPlanned RouteStatus = iota
	RouteStatusActive
	RouteStatusCompleted
	RouteStatusModified
)

type ServiceAddress struct {
	Street      string          `json:"street"`
	City        string          `json:"city"`
	PostalCode  string          `json:"postal_code"`
	Country     string          `json:"country"`
	GPS         *GPSCoordinates `json:"gps,omitempty"`
	AccessNotes string          `json:"access_notes"`
	ParkingInfo string          `json:"parking_info"`
}

type ServiceCalendar struct {
	appointments  map[string][]*ServiceAppointment
	holidays      []Holiday
	businessHours BusinessHours
	mu            sync.RWMutex
}

type Holiday struct {
	Date           time.Time `json:"date"`
	Name           string    `json:"name"`
	Type           string    `json:"type"` // national, company, seasonal
	AffectsService bool      `json:"affects_service"`
}

type BusinessHours struct {
	Monday        DayHours `json:"monday"`
	Tuesday       DayHours `json:"tuesday"`
	Wednesday     DayHours `json:"wednesday"`
	Thursday      DayHours `json:"thursday"`
	Friday        DayHours `json:"friday"`
	Saturday      DayHours `json:"saturday"`
	Sunday        DayHours `json:"sunday"`
	Emergency24x7 bool     `json:"emergency_24x7"`
}

type DayHours struct {
	Open       bool      `json:"open"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	LunchBreak *Break    `json:"lunch_break,omitempty"`
}

// NewHVACCRMSystem creates a new HVAC CRM system
func NewHVACCRMSystem(config *HVACCRMConfig, logger *zap.Logger) *HVACCRMSystem {
	if config == nil {
		config = DefaultHVACConfig()
	}

	system := &HVACCRMSystem{
		config: config,
		logger: logger,
		cache:  NewHVACRedisCache(),
	}

	// Initialize all 13 Fibonacci Era components
	system.initializeComponents()

	logger.Info("HVAC CRM System initialized",
		zap.String("company", config.CompanyName),
		zap.String("service_area", config.ServiceArea),
		zap.String("business_type", config.BusinessType),
	)

	return system
}

func DefaultHVACConfig() *HVACCRMConfig {
	return &HVACCRMConfig{
		CompanyName:        "Fulmark HVAC",
		ServiceArea:        "Warsaw Metropolitan Area",
		BusinessType:       "residential_commercial",
		SeasonalOperations: true,
		EmergencyServices:  true,
		WarrantyPeriod:     24,
		ServiceRadius:      50,
	}
}

func (h *HVACCRMSystem) initializeComponents() {
	// Task 1: Customer Lifecycle Management
	h.customerLifecycle = NewCustomerLifecycleManager()

	// Task 2: Equipment Registry
	h.equipmentRegistry = NewEquipmentRegistrySystem()

	// Task 3: Service Scheduling
	h.serviceScheduler = NewServiceSchedulingSystem()

	// Continue with remaining 10 tasks...
	h.logger.Info("All 13 Fibonacci Era HVAC CRM components initialized")
}

// HVACRedisCache for era-based caching
type HVACRedisCache struct {
	client interface{} // Redis client
	era    string
	mu     sync.RWMutex
}

func NewHVACRedisCache() *HVACRedisCache {
	return &HVACRedisCache{
		era: fmt.Sprintf("fibonacci_era_13_%d", time.Now().Unix()),
	}
}

// Customer Lifecycle Management Implementation
func NewCustomerLifecycleManager() *CustomerLifecycleManager {
	clm := &CustomerLifecycleManager{
		stages: make(map[CustomerStage]*LifecycleStage),
		rules:  make(map[string]*LifecycleRule),
	}

	// Initialize HVAC-specific lifecycle stages
	clm.initializeHVACStages()

	return clm
}

type LifecycleRule struct {
	Name      string `json:"name"`
	Condition string `json:"condition"`
	Action    string `json:"action"`
	Automated bool   `json:"automated"`
	Priority  int    `json:"priority"`
}

func (clm *CustomerLifecycleManager) initializeHVACStages() {
	// Lead Stage
	clm.stages[StageLead] = &LifecycleStage{
		Name:            "Lead",
		Description:     "Initial contact or inquiry about HVAC services",
		AverageStayDays: 3,
		NextStages:      []CustomerStage{StageProspect, StageQuoteRequested},
		Actions: []StageAction{
			{Type: "call", Description: "Initial contact call", DaysAfter: 0, Priority: "high", Automated: false},
			{Type: "email", Description: "Welcome email with company info", DaysAfter: 0, Priority: "medium", Automated: true},
		},
		Triggers: []StageTrigger{
			{Event: "equipment_failure", Condition: "emergency", Action: "escalate_to_emergency"},
		},
		KPIs: map[string]float64{
			"conversion_rate":     0.25,
			"response_time_hours": 2.0,
		},
	}

	// Prospect Stage
	clm.stages[StageProspect] = &LifecycleStage{
		Name:            "Prospect",
		Description:     "Qualified lead with genuine HVAC needs",
		AverageStayDays: 7,
		NextStages:      []CustomerStage{StageQuoteRequested, StageJobScheduled},
		Actions: []StageAction{
			{Type: "visit", Description: "On-site assessment", DaysAfter: 2, Priority: "high", Automated: false},
			{Type: "email", Description: "Follow-up with assessment results", DaysAfter: 3, Priority: "high", Automated: true},
		},
		KPIs: map[string]float64{
			"quote_conversion_rate":      0.60,
			"assessment_completion_rate": 0.85,
		},
	}

	// Continue with other stages...
}

// Equipment Registry Implementation
func NewEquipmentRegistrySystem() *EquipmentRegistrySystem {
	return &EquipmentRegistrySystem{
		equipment:            make(map[string]*HVACEquipment),
		maintenanceSchedules: make(map[string]*MaintenanceSchedule),
		warranties:           make(map[string]*WarrantyInfo),
	}
}

type MaintenanceSchedule struct {
	EquipmentID    string               `json:"equipment_id"`
	Type           MaintenanceType      `json:"type"`
	Frequency      MaintenanceFrequency `json:"frequency"`
	LastPerformed  *time.Time           `json:"last_performed,omitempty"`
	NextDue        time.Time            `json:"next_due"`
	Tasks          []MaintenanceTask    `json:"tasks"`
	SeasonalAdjust bool                 `json:"seasonal_adjust"`
	Priority       MaintenancePriority  `json:"priority"`
}

type MaintenanceType int

const (
	MaintenancePreventive MaintenanceType = iota
	MaintenancePredictive
	MaintenanceReactive
	MaintenanceSeasonal
	MaintenanceWarranty
)

type MaintenanceFrequency int

const (
	FrequencyMonthly MaintenanceFrequency = iota
	FrequencyQuarterly
	FrequencySemiAnnual
	FrequencyAnnual
	FrequencyBiAnnual
	FrequencyAsNeeded
)

type MaintenanceTask struct {
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	EstimatedTime  float64  `json:"estimated_time_hours"`
	RequiredSkills []string `json:"required_skills"`
	Tools          []string `json:"tools"`
	Parts          []string `json:"parts,omitempty"`
	Safety         []string `json:"safety_requirements"`
	Checklist      []string `json:"checklist"`
}

type MaintenancePriority int

const (
	MaintenancePriorityLow MaintenancePriority = iota
	MaintenancePriorityNormal
	MaintenancePriorityHigh
	MaintenancePriorityCritical
)

// Service Scheduling Implementation
func NewServiceSchedulingSystem() *ServiceSchedulingSystem {
	return &ServiceSchedulingSystem{
		appointments: make(map[string]*ServiceAppointment),
		technicians:  make(map[string]*Technician),
		routes:       make(map[string]*ServiceRoute),
		calendar:     NewServiceCalendar(),
	}
}

func NewServiceCalendar() *ServiceCalendar {
	return &ServiceCalendar{
		appointments: make(map[string][]*ServiceAppointment),
		holidays:     []Holiday{},
		businessHours: BusinessHours{
			Monday:        DayHours{Open: true, StartTime: time.Date(0, 0, 0, 8, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 17, 0, 0, 0, time.UTC)},
			Tuesday:       DayHours{Open: true, StartTime: time.Date(0, 0, 0, 8, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 17, 0, 0, 0, time.UTC)},
			Wednesday:     DayHours{Open: true, StartTime: time.Date(0, 0, 0, 8, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 17, 0, 0, 0, time.UTC)},
			Thursday:      DayHours{Open: true, StartTime: time.Date(0, 0, 0, 8, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 17, 0, 0, 0, time.UTC)},
			Friday:        DayHours{Open: true, StartTime: time.Date(0, 0, 0, 8, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 17, 0, 0, 0, time.UTC)},
			Saturday:      DayHours{Open: true, StartTime: time.Date(0, 0, 0, 9, 0, 0, 0, time.UTC), EndTime: time.Date(0, 0, 0, 15, 0, 0, 0, time.UTC)},
			Sunday:        DayHours{Open: false},
			Emergency24x7: true,
		},
	}
}

// Task 4: HVAC Inventory & Parts Management
type HVACInventoryManager struct {
	inventory  map[string]*InventoryItem
	suppliers  map[string]*Supplier
	orders     map[string]*PurchaseOrder
	warehouses map[string]*Warehouse
	categories map[string]*PartCategory
	mu         sync.RWMutex
}

type InventoryItem struct {
	PartNumber     string            `json:"part_number"`
	Name           string            `json:"name"`
	Description    string            `json:"description"`
	Category       string            `json:"category"`
	Brand          string            `json:"brand"`
	Model          string            `json:"model,omitempty"`
	UnitCost       float64           `json:"unit_cost"`
	SellingPrice   float64           `json:"selling_price"`
	Quantity       int               `json:"quantity"`
	MinStock       int               `json:"min_stock"`
	MaxStock       int               `json:"max_stock"`
	ReorderPoint   int               `json:"reorder_point"`
	Location       WarehouseLocation `json:"location"`
	Supplier       string            `json:"supplier"`
	LeadTimeDays   int               `json:"lead_time_days"`
	WarrantyMonths int               `json:"warranty_months"`
	Compatibility  []string          `json:"compatibility"` // Equipment models
	Seasonal       bool              `json:"seasonal"`
	Perishable     bool              `json:"perishable"`
	ExpiryDate     *time.Time        `json:"expiry_date,omitempty"`
	LastOrdered    *time.Time        `json:"last_ordered,omitempty"`
	LastUsed       *time.Time        `json:"last_used,omitempty"`
	UsageFrequency UsageFrequency    `json:"usage_frequency"`
	Status         InventoryStatus   `json:"status"`
}

type WarehouseLocation struct {
	WarehouseID string `json:"warehouse_id"`
	Aisle       string `json:"aisle"`
	Shelf       string `json:"shelf"`
	Bin         string `json:"bin"`
}

type UsageFrequency int

const (
	UsageVeryHigh UsageFrequency = iota // Daily
	UsageHigh                           // Weekly
	UsageMedium                         // Monthly
	UsageLow                            // Quarterly
	UsageVeryLow                        // Annually
	UsageRare                           // As needed
)

type InventoryStatus int

const (
	StatusInStock InventoryStatus = iota
	StatusLowStock
	StatusOutOfStock
	StatusOnOrder
	StatusDiscontinued
	StatusObsolete
)

type Supplier struct {
	ID              string          `json:"id"`
	Name            string          `json:"name"`
	ContactPerson   string          `json:"contact_person"`
	Email           string          `json:"email"`
	Phone           string          `json:"phone"`
	Address         SupplierAddress `json:"address"`
	PaymentTerms    string          `json:"payment_terms"`
	DeliveryTerms   string          `json:"delivery_terms"`
	MinOrderAmount  float64         `json:"min_order_amount"`
	DiscountTiers   []DiscountTier  `json:"discount_tiers"`
	LeadTimeDays    int             `json:"lead_time_days"`
	Rating          float64         `json:"rating"`
	Certifications  []string        `json:"certifications"`
	PreferredVendor bool            `json:"preferred_vendor"`
	Active          bool            `json:"active"`
}

type SupplierAddress struct {
	Street     string `json:"street"`
	City       string `json:"city"`
	PostalCode string `json:"postal_code"`
	Country    string `json:"country"`
}

type DiscountTier struct {
	MinQuantity int     `json:"min_quantity"`
	Discount    float64 `json:"discount_percent"`
}

type PurchaseOrder struct {
	ID            string      `json:"id"`
	SupplierID    string      `json:"supplier_id"`
	OrderDate     time.Time   `json:"order_date"`
	ExpectedDate  time.Time   `json:"expected_date"`
	Status        OrderStatus `json:"status"`
	Items         []OrderItem `json:"items"`
	SubTotal      float64     `json:"sub_total"`
	Tax           float64     `json:"tax"`
	Shipping      float64     `json:"shipping"`
	Total         float64     `json:"total"`
	Notes         string      `json:"notes"`
	CreatedBy     string      `json:"created_by"`
	ApprovedBy    string      `json:"approved_by,omitempty"`
	ReceivedDate  *time.Time  `json:"received_date,omitempty"`
	InvoiceNumber string      `json:"invoice_number,omitempty"`
}

type OrderStatus int

const (
	OrderDraft OrderStatus = iota
	OrderPending
	OrderApproved
	OrderSent
	OrderPartiallyReceived
	OrderReceived
	OrderCancelled
)

type OrderItem struct {
	PartNumber     string  `json:"part_number"`
	Description    string  `json:"description"`
	Quantity       int     `json:"quantity"`
	UnitCost       float64 `json:"unit_cost"`
	Total          float64 `json:"total"`
	ReceivedQty    int     `json:"received_qty"`
	BackorderedQty int     `json:"backordered_qty"`
}

type Warehouse struct {
	ID       string           `json:"id"`
	Name     string           `json:"name"`
	Address  WarehouseAddress `json:"address"`
	Manager  string           `json:"manager"`
	Capacity int              `json:"capacity_cubic_meters"`
	Climate  ClimateControl   `json:"climate"`
	Security SecurityFeatures `json:"security"`
	Active   bool             `json:"active"`
}

type WarehouseAddress struct {
	Street     string          `json:"street"`
	City       string          `json:"city"`
	PostalCode string          `json:"postal_code"`
	Country    string          `json:"country"`
	GPS        *GPSCoordinates `json:"gps,omitempty"`
}

type ClimateControl struct {
	Controlled  bool    `json:"controlled"`
	MinTemp     float64 `json:"min_temp_celsius"`
	MaxTemp     float64 `json:"max_temp_celsius"`
	MinHumidity float64 `json:"min_humidity_percent"`
	MaxHumidity float64 `json:"max_humidity_percent"`
}

type SecurityFeatures struct {
	AlarmSystem   bool `json:"alarm_system"`
	CCTV          bool `json:"cctv"`
	AccessControl bool `json:"access_control"`
	FireSafety    bool `json:"fire_safety"`
}

type PartCategory struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ParentID    string `json:"parent_id,omitempty"`
	Level       int    `json:"level"`
	Path        string `json:"path"`
	Active      bool   `json:"active"`
}

// Task 5: Technician Management & Skills Tracking
type TechnicianManagementSystem struct {
	technicians    map[string]*Technician
	skillMatrix    map[string]*SkillDefinition
	certifications map[string]*CertificationProgram
	training       map[string]*TrainingRecord
	performance    map[string]*PerformanceMetrics
	schedules      map[string]*TechnicianSchedule
	mu             sync.RWMutex
}

type SkillDefinition struct {
	ID                    string        `json:"id"`
	Name                  string        `json:"name"`
	Description           string        `json:"description"`
	Category              SkillCategory `json:"category"`
	Level                 SkillLevel    `json:"level"`
	Prerequisites         []string      `json:"prerequisites"`
	TrainingHours         int           `json:"training_hours"`
	CertificationRequired bool          `json:"certification_required"`
	RenewalPeriod         int           `json:"renewal_period_months"`
	Active                bool          `json:"active"`
}

type SkillCategory int

const (
	SkillHVACBasics SkillCategory = iota
	SkillElectrical
	SkillRefrigeration
	SkillHeating
	SkillVentilation
	SkillControls
	SkillDiagnostics
	SkillSafety
	SkillCustomerService
	SkillSales
)

type SkillLevel int

const (
	SkillBeginner SkillLevel = iota
	SkillIntermediate
	SkillAdvanced
	SkillExpert
	SkillMaster
)

type CertificationProgram struct {
	ID              string   `json:"id"`
	Name            string   `json:"name"`
	IssuingBody     string   `json:"issuing_body"`
	Description     string   `json:"description"`
	Requirements    []string `json:"requirements"`
	ValidityPeriod  int      `json:"validity_period_months"`
	Cost            float64  `json:"cost"`
	OnlineAvailable bool     `json:"online_available"`
	Active          bool     `json:"active"`
}

type TrainingRecord struct {
	ID             string         `json:"id"`
	TechnicianID   string         `json:"technician_id"`
	ProgramID      string         `json:"program_id"`
	StartDate      time.Time      `json:"start_date"`
	CompletionDate *time.Time     `json:"completion_date,omitempty"`
	Status         TrainingStatus `json:"status"`
	Score          float64        `json:"score"`
	Instructor     string         `json:"instructor"`
	Location       string         `json:"location"`
	Cost           float64        `json:"cost"`
	Notes          string         `json:"notes"`
}

type TrainingStatus int

const (
	TrainingScheduled TrainingStatus = iota
	TrainingInProgress
	TrainingCompleted
	TrainingFailed
	TrainingCancelled
)

type PerformanceMetrics struct {
	TechnicianID     string    `json:"technician_id"`
	Period           string    `json:"period"` // monthly, quarterly, yearly
	JobsCompleted    int       `json:"jobs_completed"`
	AverageJobTime   float64   `json:"average_job_time_hours"`
	CustomerRating   float64   `json:"customer_rating"`
	FirstTimeFixRate float64   `json:"first_time_fix_rate"`
	SafetyIncidents  int       `json:"safety_incidents"`
	TrainingHours    int       `json:"training_hours"`
	Revenue          float64   `json:"revenue"`
	CallbackRate     float64   `json:"callback_rate"`
	OnTimePercentage float64   `json:"on_time_percentage"`
	EfficiencyScore  float64   `json:"efficiency_score"`
	LastUpdated      time.Time `json:"last_updated"`
}

type TechnicianSchedule struct {
	TechnicianID   string                   `json:"technician_id"`
	WeekStarting   time.Time                `json:"week_starting"`
	Availability   WeeklyAvailability       `json:"availability"`
	Assignments    []ScheduleAssignment     `json:"assignments"`
	TimeOff        []TimeOffRequest         `json:"time_off"`
	OnCallSchedule []OnCallPeriod           `json:"on_call_schedule"`
	TravelTime     map[string]time.Duration `json:"travel_time"`
	LastUpdated    time.Time                `json:"last_updated"`
}

type ScheduleAssignment struct {
	AppointmentID string           `json:"appointment_id"`
	StartTime     time.Time        `json:"start_time"`
	EndTime       time.Time        `json:"end_time"`
	TravelTime    time.Duration    `json:"travel_time"`
	Status        AssignmentStatus `json:"status"`
}

type AssignmentStatus int

const (
	AssignmentScheduled AssignmentStatus = iota
	AssignmentConfirmed
	AssignmentInProgress
	AssignmentCompleted
	AssignmentCancelled
	AssignmentRescheduled
)

type TimeOffRequest struct {
	ID          string        `json:"id"`
	StartDate   time.Time     `json:"start_date"`
	EndDate     time.Time     `json:"end_date"`
	Type        TimeOffType   `json:"type"`
	Status      RequestStatus `json:"status"`
	Reason      string        `json:"reason"`
	ApprovedBy  string        `json:"approved_by,omitempty"`
	RequestDate time.Time     `json:"request_date"`
}

type TimeOffType int

const (
	TimeOffVacation TimeOffType = iota
	TimeOffSick
	TimeOffPersonal
	TimeOffTraining
	TimeOffEmergency
)

type RequestStatus int

const (
	RequestPending RequestStatus = iota
	RequestApproved
	RequestDenied
	RequestCancelled
)

type OnCallPeriod struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Type      string    `json:"type"` // emergency, after_hours, weekend
	Priority  int       `json:"priority"`
}

func NewHVACInventoryManager() *HVACInventoryManager {
	return &HVACInventoryManager{
		inventory:  make(map[string]*InventoryItem),
		suppliers:  make(map[string]*Supplier),
		orders:     make(map[string]*PurchaseOrder),
		warehouses: make(map[string]*Warehouse),
		categories: make(map[string]*PartCategory),
	}
}

func NewTechnicianManagementSystem() *TechnicianManagementSystem {
	return &TechnicianManagementSystem{
		technicians:    make(map[string]*Technician),
		skillMatrix:    make(map[string]*SkillDefinition),
		certifications: make(map[string]*CertificationProgram),
		training:       make(map[string]*TrainingRecord),
		performance:    make(map[string]*PerformanceMetrics),
		schedules:      make(map[string]*TechnicianSchedule),
	}
}
