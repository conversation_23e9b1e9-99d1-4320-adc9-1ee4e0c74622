package hvaccrm

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 🌀 FIBONACCI ERA 13 - FINAL: HVAC CRM COMPLETION
// Tasks 10-13: Business Intelligence, Integration Hub, Financial Management, Knowledge Base

// Task 10: HVAC Business Intelligence Dashboard
type HVACBusinessIntelligence struct {
	dashboards  map[string]*Dashboard
	reports     map[string]*BIReport
	kpis        map[string]*KPI
	datasets    map[string]*Dataset
	alerts      map[string]*BIAlert
	mu          sync.RWMutex
}

type Dashboard struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Description     string              `json:"description"`
	Category        DashboardCategory   `json:"category"`
	Widgets         []Widget            `json:"widgets"`
	Layout          DashboardLayout     `json:"layout"`
	Filters         []DashboardFilter   `json:"filters"`
	RefreshInterval time.Duration       `json:"refresh_interval"`
	Permissions     []DashboardPermission `json:"permissions"`
	CreatedBy       string              `json:"created_by"`
	CreatedDate     time.Time           `json:"created_date"`
	LastModified    time.Time           `json:"last_modified"`
	Active          bool                `json:"active"`
}

type DashboardCategory int

const (
	DashboardOperational DashboardCategory = iota
	DashboardFinancial
	DashboardCustomer
	DashboardTechnician
	DashboardCompliance
	DashboardExecutive
	DashboardMaintenance
)

type Widget struct {
	ID          string                 `json:"id"`
	Type        WidgetType             `json:"type"`
	Title       string                 `json:"title"`
	DataSource  string                 `json:"data_source"`
	Query       string                 `json:"query"`
	Config      map[string]interface{} `json:"config"`
	Position    WidgetPosition         `json:"position"`
	Size        WidgetSize             `json:"size"`
	RefreshRate time.Duration          `json:"refresh_rate"`
	Filters     []WidgetFilter         `json:"filters"`
}

type WidgetType int

const (
	WidgetChart WidgetType = iota
	WidgetTable
	WidgetMetric
	WidgetGauge
	WidgetMap
	WidgetCalendar
	WidgetList
	WidgetProgress
)

type WidgetPosition struct {
	X int `json:"x"`
	Y int `json:"y"`
}

type WidgetSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

type WidgetFilter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

type DashboardLayout struct {
	Type    LayoutType `json:"type"`
	Columns int        `json:"columns"`
	Rows    int        `json:"rows"`
	Grid    bool       `json:"grid"`
}

type LayoutType int

const (
	LayoutGrid LayoutType = iota
	LayoutFreeform
	LayoutMasonry
	LayoutTabs
)

type DashboardFilter struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Type        FilterType          `json:"type"`
	Field       string              `json:"field"`
	Options     []FilterOption      `json:"options"`
	DefaultValue interface{}        `json:"default_value"`
	Required    bool                `json:"required"`
}

type FilterType int

const (
	FilterSelect FilterType = iota
	FilterMultiSelect
	FilterDateRange
	FilterNumericRange
	FilterText
	FilterBoolean
)

type FilterOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

type DashboardPermission struct {
	UserID      string           `json:"user_id,omitempty"`
	Role        string           `json:"role,omitempty"`
	Permissions []PermissionType `json:"permissions"`
}

type PermissionType int

const (
	PermissionView PermissionType = iota
	PermissionEdit
	PermissionShare
	PermissionDelete
	PermissionExport
)

type BIReport struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Description     string              `json:"description"`
	Category        ReportCategory      `json:"category"`
	Type            ReportType          `json:"type"`
	DataSources     []string            `json:"data_sources"`
	Parameters      []ReportParameter   `json:"parameters"`
	Schedule        *ReportSchedule     `json:"schedule,omitempty"`
	Format          ReportFormat        `json:"format"`
	Recipients      []string            `json:"recipients"`
	Template        string              `json:"template"`
	LastGenerated   *time.Time          `json:"last_generated,omitempty"`
	NextGeneration  *time.Time          `json:"next_generation,omitempty"`
	Active          bool                `json:"active"`
}

type ReportCategory int

const (
	ReportOperational ReportCategory = iota
	ReportFinancial
	ReportCompliance
	ReportPerformance
	ReportCustomer
	ReportInventory
)

type ReportType int

const (
	ReportTabular ReportType = iota
	ReportChart
	ReportDashboard
	ReportSummary
	ReportDetailed
)

type ReportParameter struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	DefaultValue interface{} `json:"default_value"`
	Required     bool        `json:"required"`
	Options      []string    `json:"options,omitempty"`
}

type ReportSchedule struct {
	Frequency   ScheduleFrequency `json:"frequency"`
	Time        string            `json:"time"`
	DayOfWeek   *int              `json:"day_of_week,omitempty"`
	DayOfMonth  *int              `json:"day_of_month,omitempty"`
	Timezone    string            `json:"timezone"`
	StartDate   time.Time         `json:"start_date"`
	EndDate     *time.Time        `json:"end_date,omitempty"`
}

type ScheduleFrequency int

const (
	FrequencyDaily ScheduleFrequency = iota
	FrequencyWeekly
	FrequencyMonthly
	FrequencyQuarterly
	FrequencyYearly
)

type ReportFormat int

const (
	FormatPDF ReportFormat = iota
	FormatExcel
	FormatCSV
	FormatHTML
	FormatJSON
)

type KPI struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Description     string              `json:"description"`
	Category        KPICategory         `json:"category"`
	Metric          string              `json:"metric"`
	Target          float64             `json:"target"`
	CurrentValue    float64             `json:"current_value"`
	PreviousValue   float64             `json:"previous_value"`
	Trend           TrendDirection      `json:"trend"`
	Status          KPIStatus           `json:"status"`
	Unit            string              `json:"unit"`
	Calculation     string              `json:"calculation"`
	DataSource      string              `json:"data_source"`
	UpdateFrequency UpdateFrequency     `json:"update_frequency"`
	LastUpdated     time.Time           `json:"last_updated"`
	Thresholds      KPIThresholds       `json:"thresholds"`
}

type KPICategory int

const (
	KPIFinancial KPICategory = iota
	KPIOperational
	KPICustomer
	KPIEmployee
	KPIQuality
	KPIEfficiency
)

type KPIStatus int

const (
	KPIStatusExcellent KPIStatus = iota
	KPIStatusGood
	KPIStatusWarning
	KPIStatusCritical
	KPIStatusUnknown
)

type KPIThresholds struct {
	Excellent float64 `json:"excellent"`
	Good      float64 `json:"good"`
	Warning   float64 `json:"warning"`
	Critical  float64 `json:"critical"`
}

type Dataset struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Description     string              `json:"description"`
	Source          DataSource          `json:"source"`
	Schema          []DataField         `json:"schema"`
	RefreshSchedule *DataRefreshSchedule `json:"refresh_schedule,omitempty"`
	LastRefresh     *time.Time          `json:"last_refresh,omitempty"`
	NextRefresh     *time.Time          `json:"next_refresh,omitempty"`
	RowCount        int64               `json:"row_count"`
	Size            int64               `json:"size_bytes"`
	Status          DatasetStatus       `json:"status"`
}

type DataSource struct {
	Type           DataSourceType     `json:"type"`
	ConnectionString string           `json:"connection_string"`
	Query          string             `json:"query,omitempty"`
	Parameters     map[string]string  `json:"parameters,omitempty"`
	Authentication *DataAuthentication `json:"authentication,omitempty"`
}

type DataSourceType int

const (
	DataSourceDatabase DataSourceType = iota
	DataSourceAPI
	DataSourceFile
	DataSourceStream
	DataSourceWebhook
)

type DataAuthentication struct {
	Type     AuthType `json:"type"`
	Username string   `json:"username,omitempty"`
	Password string   `json:"password,omitempty"`
	Token    string   `json:"token,omitempty"`
	Key      string   `json:"key,omitempty"`
}

type AuthType int

const (
	AuthBasic AuthType = iota
	AuthBearer
	AuthAPIKey
	AuthOAuth
)

type DataField struct {
	Name        string    `json:"name"`
	Type        FieldType `json:"type"`
	Description string    `json:"description"`
	Required    bool      `json:"required"`
	Indexed     bool      `json:"indexed"`
}

type FieldType int

const (
	FieldString FieldType = iota
	FieldInteger
	FieldFloat
	FieldBoolean
	FieldDate
	FieldDateTime
	FieldJSON
)

type DataRefreshSchedule struct {
	Frequency ScheduleFrequency `json:"frequency"`
	Time      string            `json:"time"`
	Timezone  string            `json:"timezone"`
}

type DatasetStatus int

const (
	DatasetStatusActive DatasetStatus = iota
	DatasetStatusRefreshing
	DatasetStatusError
	DatasetStatusInactive
)

type BIAlert struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Condition   AlertCondition      `json:"condition"`
	Severity    AlertSeverity       `json:"severity"`
	Recipients  []AlertRecipient    `json:"recipients"`
	Channels    []NotificationChannel `json:"channels"`
	Frequency   AlertFrequency      `json:"frequency"`
	Status      AlertStatus         `json:"status"`
	LastTriggered *time.Time        `json:"last_triggered,omitempty"`
	TriggerCount int                `json:"trigger_count"`
}

type AlertCondition struct {
	Metric    string      `json:"metric"`
	Operator  string      `json:"operator"`
	Threshold interface{} `json:"threshold"`
	Duration  time.Duration `json:"duration"`
}

type AlertSeverity int

const (
	AlertSeverityInfo AlertSeverity = iota
	AlertSeverityWarning
	AlertSeverityError
	AlertSeverityCritical
)

type AlertRecipient struct {
	Type  RecipientType `json:"type"`
	Value string        `json:"value"`
}

type RecipientType int

const (
	RecipientUser RecipientType = iota
	RecipientRole
	RecipientEmail
	RecipientWebhook
)

type AlertFrequency int

const (
	AlertFrequencyImmediate AlertFrequency = iota
	AlertFrequencyHourly
	AlertFrequencyDaily
	AlertFrequencyWeekly
)

type AlertStatus int

const (
	AlertStatusActive AlertStatus = iota
	AlertStatusInactive
	AlertStatusTriggered
	AlertStatusSnoozed
)

// Task 11: Integration Hub (IoT, Smart Thermostats)
type HVACIntegrationHub struct {
	integrations map[string]*Integration
	devices      map[string]*IoTDevice
	protocols    map[string]*Protocol
	mappings     map[string]*DataMapping
	mu           sync.RWMutex
}

type Integration struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Type            IntegrationType     `json:"type"`
	Provider        string              `json:"provider"`
	Version         string              `json:"version"`
	Status          IntegrationStatus   `json:"status"`
	Configuration   IntegrationConfig   `json:"configuration"`
	Endpoints       []IntegrationEndpoint `json:"endpoints"`
	Authentication  IntegrationAuth     `json:"authentication"`
	DataMappings    []string            `json:"data_mappings"`
	LastSync        *time.Time          `json:"last_sync,omitempty"`
	SyncFrequency   time.Duration       `json:"sync_frequency"`
	ErrorCount      int                 `json:"error_count"`
	LastError       string              `json:"last_error,omitempty"`
}

type IntegrationType int

const (
	IntegrationIoT IntegrationType = iota
	IntegrationThermostat
	IntegrationBMS
	IntegrationUtility
	IntegrationWeather
	IntegrationAccounting
	IntegrationCRM
	IntegrationInventory
)

type IntegrationStatus int

const (
	IntegrationStatusActive IntegrationStatus = iota
	IntegrationStatusInactive
	IntegrationStatusError
	IntegrationStatusSyncing
	IntegrationStatusConfiguring
)

type IntegrationConfig struct {
	Settings    map[string]interface{} `json:"settings"`
	Credentials map[string]string      `json:"credentials"`
	Options     map[string]bool        `json:"options"`
	Limits      IntegrationLimits      `json:"limits"`
}

type IntegrationLimits struct {
	RequestsPerMinute int `json:"requests_per_minute"`
	RequestsPerHour   int `json:"requests_per_hour"`
	RequestsPerDay    int `json:"requests_per_day"`
	DataSizeLimit     int `json:"data_size_limit_mb"`
}

type IntegrationEndpoint struct {
	Name        string            `json:"name"`
	URL         string            `json:"url"`
	Method      string            `json:"method"`
	Headers     map[string]string `json:"headers"`
	Parameters  map[string]string `json:"parameters"`
	Timeout     time.Duration     `json:"timeout"`
	RetryCount  int               `json:"retry_count"`
	RetryDelay  time.Duration     `json:"retry_delay"`
}

type IntegrationAuth struct {
	Type        AuthType          `json:"type"`
	Credentials map[string]string `json:"credentials"`
	TokenURL    string            `json:"token_url,omitempty"`
	Scopes      []string          `json:"scopes,omitempty"`
	ExpiresAt   *time.Time        `json:"expires_at,omitempty"`
}

type IoTDevice struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Type            DeviceType          `json:"type"`
	Manufacturer    string              `json:"manufacturer"`
	Model           string              `json:"model"`
	SerialNumber    string              `json:"serial_number"`
	FirmwareVersion string              `json:"firmware_version"`
	Status          DeviceStatus        `json:"status"`
	Location        DeviceLocation      `json:"location"`
	Capabilities    []DeviceCapability  `json:"capabilities"`
	Sensors         []Sensor            `json:"sensors"`
	Actuators       []Actuator          `json:"actuators"`
	Communication   CommunicationConfig `json:"communication"`
	LastSeen        *time.Time          `json:"last_seen,omitempty"`
	BatteryLevel    *float64            `json:"battery_level,omitempty"`
	SignalStrength  *float64            `json:"signal_strength,omitempty"`
}

type DeviceStatus int

const (
	DeviceStatusOnline DeviceStatus = iota
	DeviceStatusOffline
	DeviceStatusError
	DeviceStatusMaintenance
	DeviceStatusSleep
)

type DeviceLocation struct {
	CustomerID  string          `json:"customer_id"`
	Building    string          `json:"building"`
	Floor       string          `json:"floor"`
	Room        string          `json:"room"`
	Zone        string          `json:"zone"`
	Coordinates *GPSCoordinates `json:"coordinates,omitempty"`
}

type DeviceCapability struct {
	Name        string                 `json:"name"`
	Type        CapabilityType         `json:"type"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Supported   bool                   `json:"supported"`
}

type CapabilityType int

const (
	CapabilityTemperature CapabilityType = iota
	CapabilityHumidity
	CapabilityPressure
	CapabilityAirQuality
	CapabilityMotion
	CapabilityOccupancy
	CapabilityLight
	CapabilitySound
	CapabilityVibration
	CapabilityEnergy
)

type Sensor struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        SensorType             `json:"type"`
	Unit        string                 `json:"unit"`
	Range       SensorRange            `json:"range"`
	Accuracy    float64                `json:"accuracy"`
	Resolution  float64                `json:"resolution"`
	SampleRate  time.Duration          `json:"sample_rate"`
	Calibration *SensorCalibration     `json:"calibration,omitempty"`
	Status      SensorStatus           `json:"status"`
	LastReading *SensorReading         `json:"last_reading,omitempty"`
}

type SensorType int

const (
	SensorTemperature SensorType = iota
	SensorHumidity
	SensorPressure
	SensorCO2
	SensorVOC
	SensorPM25
	SensorMotion
	SensorLight
	SensorSound
	SensorVibration
	SensorCurrent
	SensorVoltage
	SensorPower
)

type SensorRange struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

type SensorCalibration struct {
	Date        time.Time `json:"date"`
	Offset      float64   `json:"offset"`
	Scale       float64   `json:"scale"`
	Reference   string    `json:"reference"`
	ValidUntil  time.Time `json:"valid_until"`
}

type SensorStatus int

const (
	SensorStatusActive SensorStatus = iota
	SensorStatusInactive
	SensorStatusError
	SensorStatusCalibrating
	SensorStatusMaintenance
)

type SensorReading struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Quality   ReadingQuality `json:"quality"`
	Source    string    `json:"source"`
}

type ReadingQuality int

const (
	QualityGood ReadingQuality = iota
	QualityUncertain
	QualityBad
	QualitySubstituted
)

type Actuator struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Type        ActuatorType    `json:"type"`
	Status      ActuatorStatus  `json:"status"`
	CurrentState interface{}    `json:"current_state"`
	TargetState interface{}     `json:"target_state"`
	Range       ActuatorRange   `json:"range"`
	LastCommand *ActuatorCommand `json:"last_command,omitempty"`
}

type ActuatorType int

const (
	ActuatorRelay ActuatorType = iota
	ActuatorValve
	ActuatorDamper
	ActuatorFan
	ActuatorPump
	ActuatorHeater
	ActuatorCooler
	ActuatorThermostat
)

type ActuatorStatus int

const (
	ActuatorStatusIdle ActuatorStatus = iota
	ActuatorStatusActive
	ActuatorStatusError
	ActuatorStatusMaintenance
	ActuatorStatusOverride
)

type ActuatorRange struct {
	Min  interface{} `json:"min"`
	Max  interface{} `json:"max"`
	Step interface{} `json:"step,omitempty"`
}

type ActuatorCommand struct {
	Timestamp time.Time   `json:"timestamp"`
	Command   string      `json:"command"`
	Value     interface{} `json:"value"`
	Source    string      `json:"source"`
	Result    CommandResult `json:"result"`
}

type CommandResult int

const (
	ResultSuccess CommandResult = iota
	ResultFailed
	ResultTimeout
	ResultInvalid
)

type CommunicationConfig struct {
	Protocol    CommunicationProtocol `json:"protocol"`
	Address     string                `json:"address"`
	Port        int                   `json:"port,omitempty"`
	Encryption  bool                  `json:"encryption"`
	Compression bool                  `json:"compression"`
	Timeout     time.Duration         `json:"timeout"`
	KeepAlive   time.Duration         `json:"keep_alive"`
}

type CommunicationProtocol int

const (
	ProtocolWiFi CommunicationProtocol = iota
	ProtocolEthernet
	ProtocolZigbee
	ProtocolZWave
	ProtocolBluetooth
	ProtocolLoRa
	ProtocolModbus
	ProtocolBACnet
	ProtocolMQTT
)

type Protocol struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Version         string              `json:"version"`
	Type            ProtocolType        `json:"type"`
	Description     string              `json:"description"`
	Specification   string              `json:"specification"`
	DefaultPort     int                 `json:"default_port"`
	Security        ProtocolSecurity    `json:"security"`
	MessageFormats  []MessageFormat     `json:"message_formats"`
	ErrorCodes      map[string]string   `json:"error_codes"`
	Supported       bool                `json:"supported"`
}

type ProtocolType int

const (
	ProtocolTypeApplication ProtocolType = iota
	ProtocolTypeTransport
	ProtocolTypeNetwork
	ProtocolTypeDataLink
	ProtocolTypePhysical
)

type ProtocolSecurity struct {
	Encryption      bool     `json:"encryption"`
	Authentication  bool     `json:"authentication"`
	Authorization   bool     `json:"authorization"`
	Integrity       bool     `json:"integrity"`
	SupportedCiphers []string `json:"supported_ciphers"`
}

type MessageFormat struct {
	Name        string                 `json:"name"`
	Type        MessageType            `json:"type"`
	Structure   map[string]interface{} `json:"structure"`
	Example     string                 `json:"example"`
	Validation  string                 `json:"validation"`
}

type MessageType int

const (
	MessageTypeRequest MessageType = iota
	MessageTypeResponse
	MessageTypeEvent
	MessageTypeCommand
	MessageTypeData
)

type DataMapping struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	SourceSystem    string              `json:"source_system"`
	TargetSystem    string              `json:"target_system"`
	FieldMappings   []FieldMapping      `json:"field_mappings"`
	Transformations []DataTransformation `json:"transformations"`
	Filters         []MappingFilter     `json:"filters"`
	Active          bool                `json:"active"`
}

type FieldMapping struct {
	SourceField string      `json:"source_field"`
	TargetField string      `json:"target_field"`
	DataType    FieldType   `json:"data_type"`
	Required    bool        `json:"required"`
	DefaultValue interface{} `json:"default_value,omitempty"`
}

type DataTransformation struct {
	Type        TransformationType     `json:"type"`
	Function    string                 `json:"function"`
	Parameters  map[string]interface{} `json:"parameters"`
	Order       int                    `json:"order"`
}

type TransformationType int

const (
	TransformationConvert TransformationType = iota
	TransformationFormat
	TransformationCalculate
	TransformationLookup
	TransformationValidate
	TransformationAggregate
)

type MappingFilter struct {
	Field       string      `json:"field"`
	Operator    string      `json:"operator"`
	Value       interface{} `json:"value"`
	Condition   string      `json:"condition"` // AND, OR
}

// Task 12: Financial Management (Invoicing, Payments)
type HVACFinancialManager struct {
	invoices    map[string]*Invoice
	payments    map[string]*Payment
	estimates   map[string]*Estimate
	contracts   map[string]*Contract
	accounts    map[string]*Account
	mu          sync.RWMutex
}

// Task 13: HVAC Knowledge Base & AI Assistant
type HVACKnowledgeBase struct {
	articles    map[string]*KnowledgeArticle
	categories  map[string]*KnowledgeCategory
	faqs        map[string]*FAQ
	procedures  map[string]*Procedure
	assistant   *AIAssistant
	mu          sync.RWMutex
}

// Placeholder types for remaining components
type Invoice struct {
	ID string `json:"id"`
}

type Payment struct {
	ID string `json:"id"`
}

type Estimate struct {
	ID string `json:"id"`
}

type Contract struct {
	ID string `json:"id"`
}

type Account struct {
	ID string `json:"id"`
}

type KnowledgeArticle struct {
	ID string `json:"id"`
}

type KnowledgeCategory struct {
	ID string `json:"id"`
}

type FAQ struct {
	ID string `json:"id"`
}

type Procedure struct {
	ID string `json:"id"`
}

type AIAssistant struct {
	ID string `json:"id"`
}

type CustomerDocument struct {
	ID string `json:"id"`
}

type ServiceRequest struct {
	ID string `json:"id"`
}

// Constructor functions for remaining components
func NewHVACBusinessIntelligence() *HVACBusinessIntelligence {
	return &HVACBusinessIntelligence{
		dashboards: make(map[string]*Dashboard),
		reports:    make(map[string]*BIReport),
		kpis:       make(map[string]*KPI),
		datasets:   make(map[string]*Dataset),
		alerts:     make(map[string]*BIAlert),
	}
}

func NewHVACIntegrationHub() *HVACIntegrationHub {
	return &HVACIntegrationHub{
		integrations: make(map[string]*Integration),
		devices:      make(map[string]*IoTDevice),
		protocols:    make(map[string]*Protocol),
		mappings:     make(map[string]*DataMapping),
	}
}

func NewHVACFinancialManager() *HVACFinancialManager {
	return &HVACFinancialManager{
		invoices:  make(map[string]*Invoice),
		payments:  make(map[string]*Payment),
		estimates: make(map[string]*Estimate),
		contracts: make(map[string]*Contract),
		accounts:  make(map[string]*Account),
	}
}

func NewHVACKnowledgeBase() *HVACKnowledgeBase {
	return &HVACKnowledgeBase{
		articles:   make(map[string]*KnowledgeArticle),
		categories: make(map[string]*KnowledgeCategory),
		faqs:       make(map[string]*FAQ),
		procedures: make(map[string]*Procedure),
		assistant:  &AIAssistant{ID: "hvac_ai_assistant"},
	}
}
