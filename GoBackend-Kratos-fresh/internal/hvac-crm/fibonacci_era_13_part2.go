package hvaccrm

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 🌀 FIBONACCI ERA 13 - PART 2: REMAINING HVAC CRM COMPONENTS
// Tasks 6-13: Advanced HVAC CRM/ERP functionality

// Task 6: HVAC Quote & Proposal Generator
type HVACQuoteGenerator struct {
	templates    map[string]*QuoteTemplate
	pricingRules map[string]*PricingRule
	quotes       map[string]*Quote
	proposals    map[string]*Proposal
	mu           sync.RWMutex
}

type QuoteTemplate struct {
	ID              string              `json:"id"`
	Name            string              `json:"name"`
	Type            QuoteType           `json:"type"`
	Description     string              `json:"description"`
	Sections        []QuoteSection      `json:"sections"`
	Terms           []QuoteTerm         `json:"terms"`
	ValidityDays    int                 `json:"validity_days"`
	RequiredFields  []string            `json:"required_fields"`
	OptionalFields  []string            `json:"optional_fields"`
	Active          bool                `json:"active"`
}

type QuoteType int

const (
	QuoteInstallation QuoteType = iota
	QuoteMaintenance
	QuoteRepair
	QuoteUpgrade
	QuoteEmergency
	QuoteConsultation
	QuoteWarranty
)

type QuoteSection struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Items       []QuoteLineItem   `json:"items"`
	Subtotal    float64           `json:"subtotal"`
	Required    bool              `json:"required"`
	Order       int               `json:"order"`
}

type QuoteLineItem struct {
	ID           string  `json:"id"`
	Description  string  `json:"description"`
	Quantity     float64 `json:"quantity"`
	Unit         string  `json:"unit"`
	UnitPrice    float64 `json:"unit_price"`
	Total        float64 `json:"total"`
	Category     string  `json:"category"` // labor, parts, materials, permits
	TaxRate      float64 `json:"tax_rate"`
	DiscountRate float64 `json:"discount_rate"`
	Notes        string  `json:"notes,omitempty"`
}

type QuoteTerm struct {
	Type        TermType `json:"type"`
	Description string   `json:"description"`
	Value       string   `json:"value"`
	Required    bool     `json:"required"`
}

type TermType int

const (
	TermPayment TermType = iota
	TermWarranty
	TermDelivery
	TermInstallation
	TermMaintenance
	TermLegal
)

type PricingRule struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Type        PricingRuleType     `json:"type"`
	Conditions  []PricingCondition  `json:"conditions"`
	Action      PricingAction       `json:"action"`
	Priority    int                 `json:"priority"`
	Active      bool                `json:"active"`
	ValidFrom   time.Time           `json:"valid_from"`
	ValidTo     *time.Time          `json:"valid_to,omitempty"`
}

type PricingRuleType int

const (
	RuleDiscount PricingRuleType = iota
	RuleMarkup
	RuleFixedPrice
	RuleBundlePrice
	RuleSeasonalAdjustment
	RuleVolumeDiscount
)

type PricingCondition struct {
	Field    string      `json:"field"`    // customer_type, equipment_type, quantity, season
	Operator string      `json:"operator"` // equals, greater_than, less_than, contains
	Value    interface{} `json:"value"`
}

type PricingAction struct {
	Type       ActionType  `json:"type"`
	Value      float64     `json:"value"`
	Percentage bool        `json:"percentage"`
	MaxAmount  *float64    `json:"max_amount,omitempty"`
	MinAmount  *float64    `json:"min_amount,omitempty"`
}

type ActionType int

const (
	ActionDiscount ActionType = iota
	ActionMarkup
	ActionSetPrice
	ActionAddFee
)

type Quote struct {
	ID              string          `json:"id"`
	Number          string          `json:"number"`
	CustomerID      string          `json:"customer_id"`
	SalespersonID   string          `json:"salesperson_id"`
	Type            QuoteType       `json:"type"`
	Status          QuoteStatus     `json:"status"`
	CreatedDate     time.Time       `json:"created_date"`
	ValidUntil      time.Time       `json:"valid_until"`
	Sections        []QuoteSection  `json:"sections"`
	Subtotal        float64         `json:"subtotal"`
	Tax             float64         `json:"tax"`
	Discount        float64         `json:"discount"`
	Total           float64         `json:"total"`
	Notes           string          `json:"notes"`
	Terms           []QuoteTerm     `json:"terms"`
	Attachments     []string        `json:"attachments"`
	SentDate        *time.Time      `json:"sent_date,omitempty"`
	ViewedDate      *time.Time      `json:"viewed_date,omitempty"`
	AcceptedDate    *time.Time      `json:"accepted_date,omitempty"`
	RejectedDate    *time.Time      `json:"rejected_date,omitempty"`
	RejectionReason string          `json:"rejection_reason,omitempty"`
}

type QuoteStatus int

const (
	QuoteStatusDraft QuoteStatus = iota
	QuoteStatusSent
	QuoteStatusViewed
	QuoteStatusAccepted
	QuoteStatusRejected
	QuoteStatusExpired
	QuoteStatusRevised
)

type Proposal struct {
	ID              string            `json:"id"`
	QuoteID         string            `json:"quote_id"`
	Title           string            `json:"title"`
	Description     string            `json:"description"`
	ExecutiveSummary string           `json:"executive_summary"`
	Scope           string            `json:"scope"`
	Timeline        ProjectTimeline   `json:"timeline"`
	Investment      InvestmentSummary `json:"investment"`
	Benefits        []Benefit         `json:"benefits"`
	Risks           []Risk            `json:"risks"`
	Assumptions     []string          `json:"assumptions"`
	Deliverables    []Deliverable     `json:"deliverables"`
	Team            []TeamMember      `json:"team"`
	References      []Reference       `json:"references"`
	Appendices      []Appendix        `json:"appendices"`
}

type ProjectTimeline struct {
	StartDate   time.Time       `json:"start_date"`
	EndDate     time.Time       `json:"end_date"`
	Phases      []ProjectPhase  `json:"phases"`
	Milestones  []Milestone     `json:"milestones"`
}

type ProjectPhase struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	StartDate   time.Time `json:"start_date"`
	EndDate     time.Time `json:"end_date"`
	Duration    int       `json:"duration_days"`
	Dependencies []string `json:"dependencies"`
}

type Milestone struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Date        time.Time `json:"date"`
	Critical    bool      `json:"critical"`
}

type InvestmentSummary struct {
	TotalCost       float64           `json:"total_cost"`
	Breakdown       []CostBreakdown   `json:"breakdown"`
	PaymentSchedule []PaymentMilestone `json:"payment_schedule"`
	Financing       *FinancingOption  `json:"financing,omitempty"`
	ROI             *ROIAnalysis      `json:"roi,omitempty"`
}

type CostBreakdown struct {
	Category    string  `json:"category"`
	Amount      float64 `json:"amount"`
	Percentage  float64 `json:"percentage"`
	Description string  `json:"description"`
}

type PaymentMilestone struct {
	Name        string    `json:"name"`
	Amount      float64   `json:"amount"`
	Percentage  float64   `json:"percentage"`
	DueDate     time.Time `json:"due_date"`
	Description string    `json:"description"`
}

type FinancingOption struct {
	Provider    string  `json:"provider"`
	Type        string  `json:"type"` // loan, lease, rental
	Term        int     `json:"term_months"`
	Rate        float64 `json:"rate_percent"`
	MonthlyPayment float64 `json:"monthly_payment"`
	TotalCost   float64 `json:"total_cost"`
}

type ROIAnalysis struct {
	EnergySavings   float64 `json:"energy_savings_annual"`
	MaintenanceSavings float64 `json:"maintenance_savings_annual"`
	PaybackPeriod   float64 `json:"payback_period_years"`
	NPV             float64 `json:"npv"`
	IRR             float64 `json:"irr_percent"`
}

type Benefit struct {
	Category    string  `json:"category"`
	Description string  `json:"description"`
	Value       float64 `json:"value,omitempty"`
	Quantified  bool    `json:"quantified"`
}

type Risk struct {
	Category    string     `json:"category"`
	Description string     `json:"description"`
	Probability RiskLevel  `json:"probability"`
	Impact      RiskLevel  `json:"impact"`
	Mitigation  string     `json:"mitigation"`
}

type RiskLevel int

const (
	RiskLow RiskLevel = iota
	RiskMedium
	RiskHigh
	RiskCritical
)

type Deliverable struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	DueDate     time.Time `json:"due_date"`
	Owner       string    `json:"owner"`
	Status      string    `json:"status"`
}

type TeamMember struct {
	Name         string   `json:"name"`
	Role         string   `json:"role"`
	Experience   string   `json:"experience"`
	Certifications []string `json:"certifications"`
	Responsibilities []string `json:"responsibilities"`
}

type Reference struct {
	CustomerName string `json:"customer_name"`
	ProjectType  string `json:"project_type"`
	Year         int    `json:"year"`
	Description  string `json:"description"`
	ContactInfo  string `json:"contact_info,omitempty"`
}

type Appendix struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Content     string `json:"content"`
	FileURL     string `json:"file_url,omitempty"`
}

// Task 7: Energy Efficiency Analytics
type EnergyEfficiencyAnalytics struct {
	measurements map[string]*EnergyMeasurement
	baselines    map[string]*EnergyBaseline
	reports      map[string]*EfficiencyReport
	benchmarks   map[string]*EnergyBenchmark
	mu           sync.RWMutex
}

type EnergyMeasurement struct {
	ID              string                 `json:"id"`
	EquipmentID     string                 `json:"equipment_id"`
	CustomerID      string                 `json:"customer_id"`
	Timestamp       time.Time              `json:"timestamp"`
	Metrics         map[string]float64     `json:"metrics"`
	Temperature     TemperatureReading     `json:"temperature"`
	Humidity        float64                `json:"humidity_percent"`
	PowerConsumption float64               `json:"power_consumption_kwh"`
	Efficiency      float64                `json:"efficiency_rating"`
	COP             float64                `json:"cop"` // Coefficient of Performance
	EER             float64                `json:"eer"` // Energy Efficiency Ratio
	SEER            float64                `json:"seer"` // Seasonal Energy Efficiency Ratio
	Source          MeasurementSource      `json:"source"`
}

type TemperatureReading struct {
	Indoor    float64 `json:"indoor_celsius"`
	Outdoor   float64 `json:"outdoor_celsius"`
	Supply    float64 `json:"supply_celsius"`
	Return    float64 `json:"return_celsius"`
	Setpoint  float64 `json:"setpoint_celsius"`
}

type MeasurementSource int

const (
	SourceIoTSensor MeasurementSource = iota
	SourceManualReading
	SourceUtilityBill
	SourceSmartMeter
	SourceThermostat
)

type EnergyBaseline struct {
	ID              string                 `json:"id"`
	EquipmentID     string                 `json:"equipment_id"`
	Period          BaselinePeriod         `json:"period"`
	StartDate       time.Time              `json:"start_date"`
	EndDate         time.Time              `json:"end_date"`
	AverageConsumption float64             `json:"average_consumption_kwh"`
	PeakConsumption float64               `json:"peak_consumption_kwh"`
	EfficiencyRating float64              `json:"efficiency_rating"`
	CostPerKWh      float64               `json:"cost_per_kwh"`
	TotalCost       float64               `json:"total_cost"`
	WeatherData     WeatherBaseline       `json:"weather_data"`
	Adjustments     []BaselineAdjustment  `json:"adjustments"`
}

type BaselinePeriod int

const (
	PeriodDaily BaselinePeriod = iota
	PeriodWeekly
	PeriodMonthly
	PeriodSeasonal
	PeriodAnnual
)

type WeatherBaseline struct {
	AverageTemp     float64 `json:"average_temp_celsius"`
	HeatingDegDays  float64 `json:"heating_degree_days"`
	CoolingDegDays  float64 `json:"cooling_degree_days"`
	Humidity        float64 `json:"average_humidity_percent"`
}

type BaselineAdjustment struct {
	Type        AdjustmentType `json:"type"`
	Factor      float64        `json:"factor"`
	Description string         `json:"description"`
	Applied     bool           `json:"applied"`
}

type AdjustmentType int

const (
	AdjustmentWeather AdjustmentType = iota
	AdjustmentOccupancy
	AdjustmentEquipmentAge
	AdjustmentMaintenance
	AdjustmentUpgrade
)

type EfficiencyReport struct {
	ID              string                `json:"id"`
	CustomerID      string                `json:"customer_id"`
	Period          ReportPeriod          `json:"period"`
	StartDate       time.Time             `json:"start_date"`
	EndDate         time.Time             `json:"end_date"`
	Summary         EfficiencySummary     `json:"summary"`
	Comparisons     []EfficiencyComparison `json:"comparisons"`
	Recommendations []EfficiencyRecommendation `json:"recommendations"`
	Savings         SavingsAnalysis       `json:"savings"`
	Charts          []ChartData           `json:"charts"`
	GeneratedDate   time.Time             `json:"generated_date"`
}

type ReportPeriod int

const (
	ReportMonthly ReportPeriod = iota
	ReportQuarterly
	ReportAnnual
	ReportCustom
)

type EfficiencySummary struct {
	TotalConsumption    float64 `json:"total_consumption_kwh"`
	AverageEfficiency   float64 `json:"average_efficiency"`
	PeakDemand          float64 `json:"peak_demand_kw"`
	LoadFactor          float64 `json:"load_factor"`
	CostSavings         float64 `json:"cost_savings"`
	CO2Reduction        float64 `json:"co2_reduction_kg"`
	PerformanceGrade    string  `json:"performance_grade"`
}

type EfficiencyComparison struct {
	Type            ComparisonType `json:"type"`
	CurrentValue    float64        `json:"current_value"`
	ComparisonValue float64        `json:"comparison_value"`
	Difference      float64        `json:"difference"`
	PercentChange   float64        `json:"percent_change"`
	Trend           TrendDirection `json:"trend"`
}

type ComparisonType int

const (
	ComparisonPreviousPeriod ComparisonType = iota
	ComparisonSamePeriodLastYear
	ComparisonBaseline
	ComparisonIndustryAverage
	ComparisonBestPractice
)

type TrendDirection int

const (
	TrendImproving TrendDirection = iota
	TrendStable
	TrendDeclining
)

type EfficiencyRecommendation struct {
	ID              string                    `json:"id"`
	Category        RecommendationCategory    `json:"category"`
	Priority        RecommendationPriority    `json:"priority"`
	Title           string                    `json:"title"`
	Description     string                    `json:"description"`
	EstimatedSavings float64                  `json:"estimated_savings_annual"`
	ImplementationCost float64                `json:"implementation_cost"`
	PaybackPeriod   float64                   `json:"payback_period_years"`
	Complexity      ImplementationComplexity  `json:"complexity"`
	Timeline        string                    `json:"timeline"`
	Benefits        []string                  `json:"benefits"`
	Risks           []string                  `json:"risks"`
}

type RecommendationCategory int

const (
	RecCategoryEquipmentUpgrade RecommendationCategory = iota
	RecCategoryMaintenance
	RecCategoryOperationalChange
	RecCategoryControlOptimization
	RecCategoryInsulation
	RecCategoryRenewableEnergy
)

type RecommendationPriority int

const (
	RecPriorityLow RecommendationPriority = iota
	RecPriorityMedium
	RecPriorityHigh
	RecPriorityCritical
)

type ImplementationComplexity int

const (
	ComplexityLow ImplementationComplexity = iota
	ComplexityMedium
	ComplexityHigh
	ComplexityVeryHigh
)

type SavingsAnalysis struct {
	EnergySavings       float64 `json:"energy_savings_kwh"`
	CostSavings         float64 `json:"cost_savings"`
	DemandReduction     float64 `json:"demand_reduction_kw"`
	EfficiencyImprovement float64 `json:"efficiency_improvement_percent"`
	CO2Reduction        float64 `json:"co2_reduction_kg"`
	WaterSavings        float64 `json:"water_savings_liters"`
}

type ChartData struct {
	Type        ChartType              `json:"type"`
	Title       string                 `json:"title"`
	XAxis       string                 `json:"x_axis"`
	YAxis       string                 `json:"y_axis"`
	DataSeries  []DataSeries           `json:"data_series"`
	Annotations []ChartAnnotation      `json:"annotations"`
}

type ChartType int

const (
	ChartLine ChartType = iota
	ChartBar
	ChartPie
	ChartScatter
	ChartArea
	ChartHeatmap
)

type DataSeries struct {
	Name   string      `json:"name"`
	Data   []DataPoint `json:"data"`
	Color  string      `json:"color"`
	Type   string      `json:"type"`
}

type DataPoint struct {
	X     interface{} `json:"x"`
	Y     float64     `json:"y"`
	Label string      `json:"label,omitempty"`
}

type ChartAnnotation struct {
	X           interface{} `json:"x"`
	Y           float64     `json:"y"`
	Text        string      `json:"text"`
	Type        string      `json:"type"`
	Color       string      `json:"color"`
}

type EnergyBenchmark struct {
	ID              string          `json:"id"`
	Name            string          `json:"name"`
	Category        BenchmarkCategory `json:"category"`
	BuildingType    string          `json:"building_type"`
	ClimateZone     string          `json:"climate_zone"`
	Size            float64         `json:"size_sqm"`
	EUI             float64         `json:"eui_kwh_per_sqm"` // Energy Use Intensity
	Source          string          `json:"source"`
	Year            int             `json:"year"`
	Percentiles     map[string]float64 `json:"percentiles"`
	Active          bool            `json:"active"`
}

type BenchmarkCategory int

const (
	BenchmarkIndustry BenchmarkCategory = iota
	BenchmarkRegional
	BenchmarkNational
	BenchmarkInternational
	BenchmarkBestPractice
)

func NewHVACQuoteGenerator() *HVACQuoteGenerator {
	return &HVACQuoteGenerator{
		templates:    make(map[string]*QuoteTemplate),
		pricingRules: make(map[string]*PricingRule),
		quotes:       make(map[string]*Quote),
		proposals:    make(map[string]*Proposal),
	}
}

func NewEnergyEfficiencyAnalytics() *EnergyEfficiencyAnalytics {
	return &EnergyEfficiencyAnalytics{
		measurements: make(map[string]*EnergyMeasurement),
		baselines:    make(map[string]*EnergyBaseline),
		reports:      make(map[string]*EfficiencyReport),
		benchmarks:   make(map[string]*EnergyBenchmark),
	}
}
