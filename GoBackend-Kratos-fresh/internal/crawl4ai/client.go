package crawl4ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🕷️ Crawl4AI Client - Web Scraping Service Integration
// GoBackend-Kratos HVAC Equipment Database Builder

// Crawl4AIClient represents the client for Crawl4AI REST API
type Crawl4AIClient struct {
	baseURL    string
	httpClient *http.Client
	log        *log.Helper
	apiKey     string
}

// Crawl4AIConfig represents configuration for Crawl4AI service
type Crawl4AIConfig struct {
	BaseURL        string        `yaml:"base_url"`        // http://localhost:8000
	APIKey         string        `yaml:"api_key"`         // Optional API key
	Timeout        time.Duration `yaml:"timeout"`         // 60s
	MaxRetries     int           `yaml:"max_retries"`     // 3
	RetryDelay     time.Duration `yaml:"retry_delay"`     // 5s
	UserAgent      string        `yaml:"user_agent"`      // Custom user agent
	EnableStealth  bool          `yaml:"enable_stealth"`  // Stealth mode
	EnableCache    bool          `yaml:"enable_cache"`    // Enable caching
}

// CrawlRequest represents a request to Crawl4AI
type CrawlRequest struct {
	URL                string                 `json:"url"`
	WaitFor            int                    `json:"wait_for,omitempty"`            // Wait time in seconds
	CSS                string                 `json:"css,omitempty"`                 // CSS selector
	Screenshot         bool                   `json:"screenshot,omitempty"`          // Take screenshot
	UserAgent          string                 `json:"user_agent,omitempty"`          // Custom user agent
	Headers            map[string]string      `json:"headers,omitempty"`             // Custom headers
	Cookies            map[string]string      `json:"cookies,omitempty"`             // Custom cookies
	ExtractionStrategy *ExtractionStrategy    `json:"extraction_strategy,omitempty"` // Extraction strategy
	ChunkerStrategy    *ChunkerStrategy       `json:"chunker_strategy,omitempty"`    // Chunking strategy
	CacheMode          string                 `json:"cache_mode,omitempty"`          // Cache mode
	BypassCache        bool                   `json:"bypass_cache,omitempty"`        // Bypass cache
	ProcessIframes     bool                   `json:"process_iframes,omitempty"`     // Process iframes
	RemoveOverlays     bool                   `json:"remove_overlays,omitempty"`     // Remove overlays
	SimulateUser       bool                   `json:"simulate_user,omitempty"`       // Simulate user interaction
	OverrideNavigator  bool                   `json:"override_navigator,omitempty"`  // Override navigator
	MagicMode          bool                   `json:"magic_mode,omitempty"`          // Magic mode for better extraction
}

// ExtractionStrategy defines how to extract data from the page
type ExtractionStrategy struct {
	Type   string                 `json:"type"`   // llm, css, xpath, regex
	Config map[string]interface{} `json:"config"` // Strategy-specific configuration
}

// ChunkerStrategy defines how to chunk the extracted content
type ChunkerStrategy struct {
	Type   string                 `json:"type"`   // semantic, fixed_length, regex
	Config map[string]interface{} `json:"config"` // Strategy-specific configuration
}

// CrawlResponse represents the response from Crawl4AI
type CrawlResponse struct {
	Success     bool                   `json:"success"`
	Data        *CrawlData             `json:"data,omitempty"`
	Error       string                 `json:"error,omitempty"`
	StatusCode  int                    `json:"status_code,omitempty"`
	ProcessTime float64                `json:"process_time,omitempty"`
}

// CrawlData represents the extracted data
type CrawlData struct {
	URL             string                 `json:"url"`
	Title           string                 `json:"title"`
	Description     string                 `json:"description"`
	Keywords        []string               `json:"keywords"`
	HTML            string                 `json:"html"`
	CleanedHTML     string                 `json:"cleaned_html"`
	Markdown        string                 `json:"markdown"`
	ExtractedData   map[string]interface{} `json:"extracted_data"`
	Links           []Link                 `json:"links"`
	Images          []Image                `json:"images"`
	Screenshot      string                 `json:"screenshot,omitempty"` // Base64 encoded
	Metadata        map[string]interface{} `json:"metadata"`
	ProcessingTime  float64                `json:"processing_time"`
}

// Link represents an extracted link
type Link struct {
	URL  string `json:"url"`
	Text string `json:"text"`
	Type string `json:"type"` // internal, external, anchor
}

// Image represents an extracted image
type Image struct {
	URL    string `json:"url"`
	Alt    string `json:"alt"`
	Title  string `json:"title"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
}

// NewCrawl4AIClient creates a new Crawl4AI client
func NewCrawl4AIClient(config *Crawl4AIConfig, logger log.Logger) *Crawl4AIClient {
	if config.Timeout == 0 {
		config.Timeout = 60 * time.Second
	}
	
	return &Crawl4AIClient{
		baseURL: config.BaseURL,
		apiKey:  config.APIKey,
		log:     log.NewHelper(logger),
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// CrawlURL crawls a single URL and returns extracted data
func (c *Crawl4AIClient) CrawlURL(ctx context.Context, req *CrawlRequest) (*CrawlResponse, error) {
	c.log.WithContext(ctx).Infof("Crawling URL: %s", req.URL)
	
	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}
	
	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/crawl", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	if c.apiKey != "" {
		httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	}
	
	// Make request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	
	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	
	// Parse response
	var crawlResp CrawlResponse
	if err := json.Unmarshal(body, &crawlResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}
	
	if !crawlResp.Success {
		return &crawlResp, fmt.Errorf("crawl failed: %s", crawlResp.Error)
	}
	
	c.log.WithContext(ctx).Infof("Successfully crawled URL: %s (%.2fs)", req.URL, crawlResp.ProcessTime)
	return &crawlResp, nil
}

// CrawlHVACProductPage crawls an HVAC product page with optimized extraction
func (c *Crawl4AIClient) CrawlHVACProductPage(ctx context.Context, url string) (*CrawlResponse, error) {
	req := &CrawlRequest{
		URL:            url,
		WaitFor:        3,
		Screenshot:     true,
		ProcessIframes: false,
		RemoveOverlays: true,
		SimulateUser:   true,
		MagicMode:      true,
		ExtractionStrategy: &ExtractionStrategy{
			Type: "llm",
			Config: map[string]interface{}{
				"prompt": `Extract HVAC equipment specifications including:
				- Model number and product name
				- Cooling/heating capacity (BTU)
				- Energy efficiency ratings (SEER, EER, HSPF, COP)
				- Physical dimensions and weight
				- Electrical specifications
				- Features and certifications
				- Pricing information if available
				- Technical documentation links
				- Product images
				Return as structured JSON.`,
			},
		},
		ChunkerStrategy: &ChunkerStrategy{
			Type: "semantic",
			Config: map[string]interface{}{
				"chunk_size": 1000,
			},
		},
	}
	
	return c.CrawlURL(ctx, req)
}

// CrawlManufacturerCatalog crawls a manufacturer's product catalog
func (c *Crawl4AIClient) CrawlManufacturerCatalog(ctx context.Context, catalogURL string, maxPages int) ([]*CrawlResponse, error) {
	c.log.WithContext(ctx).Infof("Crawling manufacturer catalog: %s", catalogURL)
	
	var results []*CrawlResponse
	
	// First, crawl the main catalog page to find product links
	mainReq := &CrawlRequest{
		URL:         catalogURL,
		WaitFor:     5,
		MagicMode:   true,
		CSS:         "a[href*='product'], a[href*='model'], .product-link",
		SimulateUser: true,
		ExtractionStrategy: &ExtractionStrategy{
			Type: "css",
			Config: map[string]interface{}{
				"selectors": map[string]string{
					"product_links": "a[href*='product'], a[href*='model'], .product-link",
					"product_names": ".product-name, .model-name, h3, h4",
					"product_images": "img[src*='product'], .product-image img",
				},
			},
		},
	}
	
	mainResp, err := c.CrawlURL(ctx, mainReq)
	if err != nil {
		return nil, fmt.Errorf("failed to crawl main catalog: %w", err)
	}
	
	results = append(results, mainResp)
	
	// Extract product links from the main page
	productLinks := c.extractProductLinks(mainResp.Data)
	
	// Limit the number of pages to crawl
	if len(productLinks) > maxPages {
		productLinks = productLinks[:maxPages]
	}
	
	// Crawl individual product pages
	for i, link := range productLinks {
		if i >= maxPages {
			break
		}
		
		c.log.WithContext(ctx).Infof("Crawling product page %d/%d: %s", i+1, len(productLinks), link)
		
		productResp, err := c.CrawlHVACProductPage(ctx, link)
		if err != nil {
			c.log.WithContext(ctx).Warnf("Failed to crawl product page %s: %v", link, err)
			continue
		}
		
		results = append(results, productResp)
		
		// Add delay between requests to be respectful
		time.Sleep(2 * time.Second)
	}
	
	c.log.WithContext(ctx).Infof("Completed crawling catalog: %d pages processed", len(results))
	return results, nil
}

// extractProductLinks extracts product page URLs from catalog data
func (c *Crawl4AIClient) extractProductLinks(data *CrawlData) []string {
	var links []string
	
	if data == nil {
		return links
	}
	
	// Extract from links
	for _, link := range data.Links {
		if c.isProductLink(link.URL) {
			links = append(links, link.URL)
		}
	}
	
	// Extract from extracted data if available
	if extractedData, ok := data.ExtractedData["product_links"]; ok {
		if linkList, ok := extractedData.([]interface{}); ok {
			for _, linkInterface := range linkList {
				if linkStr, ok := linkInterface.(string); ok && c.isProductLink(linkStr) {
					links = append(links, linkStr)
				}
			}
		}
	}
	
	return c.deduplicateLinks(links)
}

// isProductLink checks if a URL is likely a product page
func (c *Crawl4AIClient) isProductLink(url string) bool {
	productIndicators := []string{
		"/product/", "/model/", "/equipment/", "/unit/",
		"product-", "model-", "spec-", "datasheet",
	}
	
	for _, indicator := range productIndicators {
		if contains(url, indicator) {
			return true
		}
	}
	
	return false
}

// deduplicateLinks removes duplicate URLs
func (c *Crawl4AIClient) deduplicateLinks(links []string) []string {
	seen := make(map[string]bool)
	var result []string
	
	for _, link := range links {
		if !seen[link] {
			seen[link] = true
			result = append(result, link)
		}
	}
	
	return result
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr || 
		     findInString(s, substr)))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// HealthCheck checks if Crawl4AI service is available
func (c *Crawl4AIClient) HealthCheck(ctx context.Context) error {
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/health", nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}
	
	return nil
}
