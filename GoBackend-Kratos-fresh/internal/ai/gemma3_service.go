package ai

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"gobackend-hvac-kratos/internal/conf"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"strings"
	"time"

	"golang.org/x/image/draw"

	"github.com/go-kratos/kratos/v2/log"
)

// 🤖 Gemma 3 Advanced AI Service for HVAC Email Intelligence
type Gemma3Service struct {
	log        *log.Helper
	config     *Gemma3Config
	httpClient *http.Client
}

// ⚙️ Gemma 3 Configuration
type Gemma3Config struct {
	OllamaURL       string        `yaml:"ollama_url"`
	ModelName       string        `yaml:"model_name"`       // gemma3:4b-instruct
	MaxTokens       int           `yaml:"max_tokens"`       // 8192
	Temperature     float64       `yaml:"temperature"`      // 0.7
	TopP            float64       `yaml:"top_p"`            // 0.9
	ContextWindow   int           `yaml:"context_window"`   // 128000
	Timeout         time.Duration `yaml:"timeout"`          // 60s
	RetryAttempts   int           `yaml:"retry_attempts"`   // 3
	EnableVision    bool          `yaml:"enable:"`          // true for multimodal
	ImageResolution int           `yaml:"image_resolution"` // 896
}

// 📧 HVAC Email Analysis Request
type HVACEmailAnalysisRequest struct {
	EmailContent string            `json:"email_content"`
	Subject      string            `json:"subject"`
	From         string            `json:"from"`
	To           []string          `json:"to"`
	Attachments  []*AttachmentData `json:"attachments,omitempty"`
	Images       []*ImageData      `json:"images,omitempty"` // Existing field, will be deprecated
	RawImages    []*struct {
		Data        []byte `json:"data"`
		Filename    string `json:"filename"`
		ContentType string `json:"content_type"`
	} `json:"raw_images,omitempty"` // New field for raw image data
	AnalysisType string           `json:"analysis_type"` // "comprehensive", "quick", "priority"
	HVACContext  *HVACContextData `json:"hvac_context,omitempty"`
}

// 🏭 HVAC Equipment Analysis Request
type HVACEquipmentAnalysisRequest struct {
	Content      string       `json:"content"`          // Markdown content from crawling
	URL          string       `json:"url"`              // Source URL
	Title        string       `json:"title"`            // Page title
	Images       []*ImageData `json:"images,omitempty"` // Product images
	AnalysisType string       `json:"analysis_type"`    // equipment_extraction, compatibility_check, etc.
}

// 🏭 HVAC Equipment Analysis Response
type HVACEquipmentAnalysisResponse struct {
	EquipmentSpecs  *EquipmentSpecifications     `json:"equipment_specs"`
	TechnicalData   *TechnicalSpecifications     `json:"technical_data"`
	PerformanceData *EquipmentPerformanceMetrics `json:"performance_data"`
	ComplianceData  *ComplianceInformation       `json:"compliance_data"`
	ImageAnalysis   []*ImageAnalysisResult       `json:"image_analysis,omitempty"`
	DataQuality     *DataQualityAssessment       `json:"data_quality"`
	ProcessingTime  float64                      `json:"processing_time"`
	ConfidenceScore float64                      `json:"confidence_score"`
}

// 🔧 Equipment Specifications
type EquipmentSpecifications struct {
	ModelNumber     string   `json:"model_number"`
	ProductName     string   `json:"product_name"`
	ProductFamily   string   `json:"product_family"`
	Category        string   `json:"category"`
	SubCategory     string   `json:"sub_category"`
	Manufacturer    string   `json:"manufacturer"`
	CoolingCapacity string   `json:"cooling_capacity"`
	HeatingCapacity string   `json:"heating_capacity"`
	SEER            string   `json:"seer"`
	EER             string   `json:"eer"`
	HSPF            string   `json:"hspf"`
	COP             string   `json:"cop"`
	Features        []string `json:"features"`
	Applications    []string `json:"applications"`
}

// 📊 Technical Specifications
type TechnicalSpecifications struct {
	Dimensions        map[string]string `json:"dimensions"`
	Weight            string            `json:"weight"`
	ElectricalData    map[string]string `json:"electrical_data"`
	RefrigerantType   string            `json:"refrigerant_type"`
	RefrigerantCharge string            `json:"refrigerant_charge"`
	OperatingRange    map[string]string `json:"operating_range"`
	NoiseLevel        string            `json:"noise_level"`
	AirflowRating     string            `json:"airflow_rating"`
}

// ⚡ Equipment Performance Metrics
type EquipmentPerformanceMetrics struct {
	EfficiencyRatings map[string]string `json:"efficiency_ratings"`
	CapacityRatings   map[string]string `json:"capacity_ratings"`
	OperatingCosts    map[string]string `json:"operating_costs"`
	PerformanceCurves []string          `json:"performance_curves"`
	TestConditions    map[string]string `json:"test_conditions"`
}

// 📋 Compliance Information
type ComplianceInformation struct {
	AHRINumber      string   `json:"ahri_number"`
	EnergyStar      bool     `json:"energy_star"`
	Certifications  []string `json:"certifications"`
	Standards       []string `json:"standards"`
	WarrantyInfo    string   `json:"warranty_info"`
	InstallationReq []string `json:"installation_requirements"`
}

// 📊 Data Quality Assessment
type DataQualityAssessment struct {
	OverallScore      float64            `json:"overall_score"`
	CompletenessScore float64            `json:"completeness_score"`
	AccuracyScore     float64            `json:"accuracy_score"`
	MissingFields     []string           `json:"missing_fields"`
	ConfidenceByField map[string]float64 `json:"confidence_by_field"`
	DataSources       []string           `json:"data_sources"`
}

// 📎 Attachment Data
type AttachmentData struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Content     string `json:"content"` // Text content or base64 for binary
	Size        int64  `json:"size"`
}

// 🖼️ Image Data for Multimodal Analysis
type ImageData struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Base64Data  string `json:"base64_data"` // Base64 encoded image
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Description string `json:"description,omitempty"`
}

// 🏠 HVAC Context Data
type HVACContextData struct {
	CustomerHistory []string `json:"customer_history,omitempty"`
	ServiceType     string   `json:"service_type,omitempty"`
	Equipment       []string `json:"equipment,omitempty"`
	Location        string   `json:"location,omitempty"`
	SeasonalContext string   `json:"seasonal_context,omitempty"`
	BusinessHours   string   `json:"business_hours,omitempty"`
}

// 🎯 Comprehensive HVAC Analysis Response
type HVACAnalysisResponse struct {
	// Core Analysis
	Summary            string                 `json:"summary"`
	HVACRelevance      *HVACRelevanceAnalysis `json:"hvac_relevance"`
	SentimentAnalysis  *SentimentAnalysis     `json:"sentiment_analysis"`
	PriorityAssessment *PriorityAssessment    `json:"priority_assessment"`

	// Advanced Analysis
	TechnicalAnalysis *TechnicalAnalysis `json:"technical_analysis"`
	CustomerAnalysis  *CustomerAnalysis  `json:"customer_analysis"`
	ActionPlan        *ActionPlan        `json:"action_plan"`

	// Multimodal Analysis
	ImageAnalysis   []*ImageAnalysisResult `json:"image_analysis,omitempty"`
	DiagramAnalysis *DiagramAnalysis       `json:"diagram_analysis,omitempty"`

	// Business Intelligence
	BusinessInsights    *BusinessInsights `json:"business_insights"`
	RecommendedResponse string            `json:"recommended_response"`

	// Metadata
	ProcessingTime  time.Duration `json:"processing_time"`
	ModelUsed       string        `json:"model_used"`
	ConfidenceScore float64       `json:"confidence_score"`
	TokensUsed      int           `json:"tokens_used"`
}

// 🔧 HVAC Relevance Analysis
type HVACRelevanceAnalysis struct {
	IsHVACRelated      bool     `json:"is_hvac_related"`
	Confidence         float64  `json:"confidence"`
	HVACKeywords       []string `json:"hvac_keywords"`
	ServiceCategory    string   `json:"service_category"` // repair, maintenance, installation, emergency
	EquipmentMentioned []string `json:"equipment_mentioned"`
	SystemType         string   `json:"system_type"`   // heating, cooling, ventilation, combined
	UrgencyLevel       string   `json:"urgency_level"` // emergency, urgent, normal, low
	SeasonalRelevance  string   `json:"seasonal_relevance"`
	EstimatedCost      string   `json:"estimated_cost,omitempty"` // Added for consistency
}

// 💭 Advanced Sentiment Analysis
type SentimentAnalysis struct {
	OverallSentiment     string   `json:"overall_sentiment"`     // positive, negative, neutral
	SentimentScore       float64  `json:"sentiment_score"`       // -1.0 to 1.0
	EmotionalTone        []string `json:"emotional_tone"`        // frustrated, satisfied, concerned, etc.
	CustomerSatisfaction string   `json:"customer_satisfaction"` // very_satisfied, satisfied, neutral, dissatisfied, very_dissatisfied
	UrgencyIndicators    []string `json:"urgency_indicators"`
	PolitenessLevel      string   `json:"politeness_level"`
}

// ⚡ Priority Assessment
type PriorityAssessment struct {
	PriorityLevel      string   `json:"priority_level"`       // critical, high, medium, low
	PriorityScore      float64  `json:"priority_score"`       // 0-100
	ResponseTimeTarget string   `json:"response_time_target"` // immediate, 2h, 24h, 48h
	EscalationNeeded   bool     `json:"escalation_needed"`
	ReasonForPriority  []string `json:"reason_for_priority"`
	BusinessImpact     string   `json:"business_impact"`
}

// 🔬 Technical Analysis
type TechnicalAnalysis struct {
	TechnicalTerms      []string `json:"technical_terms"`
	DiagnosticClues     []string `json:"diagnostic_clues"`
	PossibleIssues      []string `json:"possible_issues"`
	RequiredExpertise   string   `json:"required_expertise"`   // basic, intermediate, advanced, specialist
	EstimatedComplexity string   `json:"estimated_complexity"` // simple, moderate, complex, very_complex
	PartsNeeded         []string `json:"parts_needed,omitempty"`
	ToolsRequired       []string `json:"tools_required,omitempty"`
}

// 👤 Customer Analysis
type CustomerAnalysis struct {
	CustomerType         string   `json:"customer_type"`       // residential, commercial, industrial
	CommunicationStyle   string   `json:"communication_style"` // direct, detailed, brief, technical
	KnowledgeLevel       string   `json:"knowledge_level"`     // novice, basic, intermediate, expert
	PreferredContact     string   `json:"preferred_contact"`   // email, phone, text, in_person
	CustomerConcerns     []string `json:"customer_concerns"`
	PreviousInteractions string   `json:"previous_interactions"`
}

// 📋 Action Plan
type ActionPlan struct {
	ImmediateActions   []string `json:"immediate_actions"`
	ScheduledActions   []string `json:"scheduled_actions"`
	FollowUpRequired   bool     `json:"follow_up_required"`
	FollowUpTimeframe  string   `json:"follow_up_timeframe"`
	AssignedTechnician string   `json:"assigned_technician,omitempty"`
	EstimatedDuration  string   `json:"estimated_duration"`
	EstimatedCost      string   `json:"estimated_cost,omitempty"`
	MaterialsNeeded    []string `json:"materials_needed,omitempty"`
}

// 🖼️ Image Analysis Result
type ImageAnalysisResult struct {
	ImageFilename    string   `json:"image_filename"`
	ImageType        string   `json:"image_type"` // diagram, photo, screenshot, document
	Description      string   `json:"description"`
	HVACElements     []string `json:"hvac_elements"` // equipment, components, systems
	TechnicalDetails []string `json:"technical_details"`
	IssuesIdentified []string `json:"issues_identified"`
	Recommendations  []string `json:"recommendations"`
	ConfidenceScore  float64  `json:"confidence_score"`
}

// 📊 Diagram Analysis
type DiagramAnalysis struct {
	DiagramType      string   `json:"diagram_type"` // schematic, floor_plan, system_layout
	SystemComponents []string `json:"system_components"`
	ConnectionPoints []string `json:"connection_points"`
	PotentialIssues  []string `json:"potential_issues"`
	ComplianceCheck  string   `json:"compliance_check"` // compliant, non_compliant, needs_review
	Recommendations  []string `json:"recommendations"`
}

// 💼 Business Insights
type BusinessInsights struct {
	RevenueOpportunity  string   `json:"revenue_opportunity"` // high, medium, low
	ServiceUpsell       []string `json:"service_upsell"`
	MaintenanceContract bool     `json:"maintenance_contract"`
	CustomerRetention   string   `json:"customer_retention"` // high_risk, medium_risk, low_risk
	CompetitiveFactors  []string `json:"competitive_factors"`
	SeasonalTrends      string   `json:"seasonal_trends"`
}

// NewGemma3Service creates a new Gemma 3 AI service
func NewGemma3Service(confModel *conf.AI_Model, logger log.Logger) *Gemma3Service {
	// Default values for Gemma3Config
	config := &Gemma3Config{
		OllamaURL:       confModel.GetEndpoint(),
		ModelName:       confModel.GetModelName(),
		MaxTokens:       int(confModel.GetMaxTokens()),
		Temperature:     0.7,
		TopP:            0.9,
		ContextWindow:   128000,
		Timeout:         60 * time.Second,
		RetryAttempts:   3,
		EnableVision:    true,
		ImageResolution: 896,
	}

	return &Gemma3Service{
		log:    log.NewHelper(logger),
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// 🏭 HVAC Equipment Analysis for Catalog Building
func (s *Gemma3Service) AnalyzeHVACEquipment(ctx context.Context, req *HVACEquipmentAnalysisRequest) (*HVACEquipmentAnalysisResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("Starting HVAC equipment analysis for: %s", req.URL)

	// Build equipment analysis prompt
	prompt := s.buildEquipmentAnalysisPrompt(req)

	// Prepare Ollama request
	ollamaReq := &OllamaRequest{
		Model:  s.config.ModelName,
		Prompt: prompt,
		Options: map[string]any{
			"temperature": 0.3, // Lower temperature for more precise extraction
			"top_p":       0.9,
			"num_predict": s.config.MaxTokens,
		},
		Stream: false,
	}

	// Add images if available
	if len(req.Images) > 0 {
		var processedImages []string
		for _, img := range req.Images {
			if img.Base64Data != "" {
				processedImages = append(processedImages, img.Base64Data)
			}
		}
		ollamaReq.Images = processedImages
	}

	// Make request to Ollama
	response, err := s.makeOllamaRequest(ctx, ollamaReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get Ollama response: %w", err)
	}

	// Parse equipment analysis response
	analysisResponse, err := s.parseEquipmentAnalysisResponse(response.Response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse equipment analysis response: %w", err)
	}

	// Calculate processing time
	analysisResponse.ProcessingTime = time.Since(startTime).Seconds()

	s.log.WithContext(ctx).Infof("Completed equipment analysis in %.2fs", analysisResponse.ProcessingTime)
	return analysisResponse, nil
}

// 🎯 Comprehensive HVAC Email Analysis
func (s *Gemma3Service) AnalyzeHVACEmail(ctx context.Context, req *HVACEmailAnalysisRequest) (*HVACAnalysisResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("Starting comprehensive HVAC analysis for email: %s", req.Subject)

	// Build comprehensive prompt
	prompt := s.buildComprehensivePrompt(req)

	// Prepare Ollama request
	ollamaReq := &OllamaRequest{
		Model:  s.config.ModelName,
		Prompt: prompt,
		Options: map[string]any{
			"temperature": s.config.Temperature,
			"top_p":       s.config.TopP,
			"num_predict": s.config.MaxTokens,
		},
		Stream: false,
	}

	// Process and add images for multimodal analysis
	var processedImages []string
	if s.config.EnableVision {
		if len(req.RawImages) > 0 {
			for _, rawImg := range req.RawImages {
				imgData, err := s.ProcessImageForAnalysis(ctx, rawImg.Data, rawImg.Filename)
				if err != nil {
					s.log.WithContext(ctx).Errorf("Failed to process raw image %s: %v", rawImg.Filename, err)
					// Decide whether to return error or continue without this image
					continue
				}
				processedImages = append(processedImages, imgData.Base64Data)
			}
		} else if len(req.Images) > 0 {
			// Fallback for existing 'Images' field (deprecated)
			s.log.WithContext(ctx).Warnf("Using deprecated 'Images' field for analysis. Please switch to 'RawImages'.")
			for _, img := range req.Images {
				processedImages = append(processedImages, img.Base64Data)
			}
		}
	}
	ollamaReq.Images = processedImages

	// Make request to Ollama
	response, err := s.makeOllamaRequest(ctx, ollamaReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get Ollama response: %w", err)
	}

	// Parse structured response
	analysisResponse, err := s.parseAnalysisResponse(response.Response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	// Add metadata
	analysisResponse.ProcessingTime = time.Since(startTime)
	analysisResponse.ModelUsed = s.config.ModelName
	analysisResponse.TokensUsed = response.TotalDuration // Approximate

	s.log.WithContext(ctx).Infof("HVAC analysis completed in %v", analysisResponse.ProcessingTime)
	return analysisResponse, nil
}

// 🔨 Build comprehensive analysis prompt
func (s *Gemma3Service) buildComprehensivePrompt(req *HVACEmailAnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString("# HVAC Email Intelligence Analysis\n\n")
	prompt.WriteString("You are an expert HVAC (Heating, Ventilation, Air Conditioning) analyst with 20+ years of experience.\n")
	prompt.WriteString("Analyze this email comprehensively and provide structured insights.\n\n")

	// Email content
	prompt.WriteString("## Email Details:\n")
	prompt.WriteString(fmt.Sprintf("**Subject:** %s\n", req.Subject))
	prompt.WriteString(fmt.Sprintf("**From:** %s\n", req.From))
	prompt.WriteString(fmt.Sprintf("**To:** %s\n", strings.Join(req.To, ", ")))
	prompt.WriteString(fmt.Sprintf("**Content:**\n%s\n\n", req.EmailContent))

	// Attachments
	if len(req.Attachments) > 0 {
		prompt.WriteString("## Attachments:\n")
		for _, att := range req.Attachments {
			prompt.WriteString(fmt.Sprintf("**%s** (%s):\n%s\n\n", att.Filename, att.ContentType, att.Content))
		}
	}

	// Images
	if len(req.Images) > 0 {
		prompt.WriteString("## Images for Analysis:\n")
		for _, img := range req.Images {
			prompt.WriteString(fmt.Sprintf("**%s** - %s\n", img.Filename, img.Description))
		}
		prompt.WriteString("\n")
	}

	// HVAC Context
	if req.HVACContext != nil {
		prompt.WriteString("## HVAC Context:\n")
		if req.HVACContext.ServiceType != "" {
			prompt.WriteString(fmt.Sprintf("**Service Type:** %s\n", req.HVACContext.ServiceType))
		}
		if len(req.HVACContext.Equipment) > 0 {
			prompt.WriteString(fmt.Sprintf("**Equipment:** %s\n", strings.Join(req.HVACContext.Equipment, ", ")))
		}
		if req.HVACContext.SeasonalContext != "" {
			prompt.WriteString(fmt.Sprintf("**Seasonal Context:** %s\n", req.HVACContext.SeasonalContext))
		}
		prompt.WriteString("\n")
	}

	// Analysis instructions
	prompt.WriteString("## Analysis Required:\n")
	prompt.WriteString("Provide a comprehensive JSON response with the following structure:\n\n")
	prompt.WriteString("```json\n")
	prompt.WriteString("{\n")
	prompt.WriteString("  \"summary\": \"Brief 2-3 sentence summary\",\n")
	prompt.WriteString("  \"hvac_relevance\": {\n")
	prompt.WriteString("    \"is_hvac_related\": true/false,\n")
	prompt.WriteString("    \"confidence\": 0.0-1.0,\n")
	prompt.WriteString("    \"hvac_keywords\": [\"keyword1\", \"keyword2\"],\n")
	prompt.WriteString("    \"service_category\": \"repair|maintenance|installation|emergency\",\n")
	prompt.WriteString("    \"equipment_mentioned\": [\"equipment1\", \"equipment2\"],\n")
	prompt.WriteString("    \"system_type\": \"heating|cooling|ventilation|combined\",\n")
	prompt.WriteString("    \"urgency_level\": \"emergency|urgent|normal|low\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"sentiment_analysis\": {\n")
	prompt.WriteString("    \"overall_sentiment\": \"positive|negative|neutral\",\n")
	prompt.WriteString("    \"sentiment_score\": -1.0 to 1.0,\n")
	prompt.WriteString("    \"emotional_tone\": [\"frustrated\", \"satisfied\"],\n")
	prompt.WriteString("    \"customer_satisfaction\": \"very_satisfied|satisfied|neutral|dissatisfied|very_dissatisfied\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"priority_assessment\": {\n")
	prompt.WriteString("    \"priority_level\": \"critical|high|medium|low\",\n")
	prompt.WriteString("    \"priority_score\": 0-100,\n")
	prompt.WriteString("    \"response_time_target\": \"immediate|2h|24h|48h\",\n")
	prompt.WriteString("    \"escalation_needed\": true/false\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"technical_analysis\": {\n")
	prompt.WriteString("    \"technical_terms\": [\"term1\", \"term2\"],\n")
	prompt.WriteString("    \"diagnostic_clues\": [\"clue1\", \"clue2\"],\n")
	prompt.WriteString("    \"possible_issues\": [\"issue1\", \"issue2\"],\n")
	prompt.WriteString("    \"required_expertise\": \"basic|intermediate|advanced|specialist\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"action_plan\": {\n")
	prompt.WriteString("    \"immediate_actions\": [\"action1\", \"action2\"],\n")
	prompt.WriteString("    \"scheduled_actions\": [\"action1\", \"action2\"],\n")
	prompt.WriteString("    \"follow_up_required\": true/false,\n")
	prompt.WriteString("    \"estimated_duration\": \"time estimate\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"business_insights\": {\n")
	prompt.WriteString("    \"revenue_opportunity\": \"high|medium|low\",\n")
	prompt.WriteString("    \"service_upsell\": [\"service1\", \"service2\"],\n")
	prompt.WriteString("    \"maintenance_contract\": true/false\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"recommended_response\": \"Professional response template\"\n")
	prompt.WriteString("}\n")
	prompt.WriteString("```\n\n")

	prompt.WriteString("Focus on HVAC-specific insights, technical accuracy, and actionable business intelligence.\n")
	prompt.WriteString("If images are provided, analyze them for HVAC equipment, diagrams, or technical details.\n")

	return prompt.String()
}

// 🌐 Ollama Request Structure
type OllamaRequest struct {
	Model   string         `json:"model"`
	Prompt  string         `json:"prompt"`
	Images  []string       `json:"images,omitempty"`
	Options map[string]any `json:"options,omitempty"`
	Stream  bool           `json:"stream"`
}

// 📥 Ollama Response Structure
type OllamaResponse struct {
	Response      string `json:"response"`
	TotalDuration int    `json:"total_duration"`
	LoadDuration  int    `json:"load_duration"`
	Done          bool   `json:"done"`
}

// 🌐 Make request to Ollama
func (s *Gemma3Service) makeOllamaRequest(ctx context.Context, req *OllamaRequest) (*OllamaResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.config.OllamaURL+"/api/generate", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("ollama request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var ollamaResp OllamaResponse
	if err := json.NewDecoder(resp.Body).Decode(&ollamaResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &ollamaResp, nil
}

// 📊 Parse structured analysis response
func (s *Gemma3Service) parseAnalysisResponse(response string) (*HVACAnalysisResponse, error) {
	// Extract JSON from response (handle markdown code blocks)
	jsonStart := strings.Index(response, "{")
	jsonEnd := strings.LastIndex(response, "}")

	if jsonStart == -1 || jsonEnd == -1 {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonStr := response[jsonStart : jsonEnd+1]

	var analysisResp HVACAnalysisResponse
	if err := json.Unmarshal([]byte(jsonStr), &analysisResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON response: %w", err)
	}

	return &analysisResp, nil
}

// 🖼️ Process image for multimodal analysis
func (s *Gemma3Service) ProcessImageForAnalysis(ctx context.Context, imageData []byte, filename string) (*ImageData, error) {
	// Decode image to get dimensions
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	targetResolution := s.config.ImageResolution
	var resizedImage image.Image = img

	// Resize to targetResolution x targetResolution if needed (Gemma 3 requirement)
	if originalWidth != targetResolution || originalHeight != targetResolution {
		s.log.WithContext(ctx).Infof("Resizing image %s from %dx%d to %dx%d",
			filename, originalWidth, originalHeight, targetResolution, targetResolution)

		dst := image.NewRGBA(image.Rect(0, 0, targetResolution, targetResolution))
		draw.NearestNeighbor.Scale(dst, dst.Bounds(), img, img.Bounds(), draw.Src, nil)
		resizedImage = dst

		// Re-encode the resized image to bytes
		buf := new(bytes.Buffer)
		switch format {
		case "jpeg":
			err = jpeg.Encode(buf, resizedImage, nil)
		case "png":
			err = png.Encode(buf, resizedImage)
		case "gif":
			err = gif.Encode(buf, resizedImage, nil)
		default:
			return nil, fmt.Errorf("unsupported image format for resizing: %s", format)
		}
		if err != nil {
			return nil, fmt.Errorf("failed to encode resized image: %w", err)
		}
		imageData = buf.Bytes()
	}

	// Encode to base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)

	// Update width and height to reflect the (potentially) resized dimensions
	width := resizedImage.Bounds().Dx()
	height := resizedImage.Bounds().Dy()

	return &ImageData{
		Filename:    filename,
		ContentType: "image/" + format,
		Base64Data:  base64Data,
		Width:       width,
		Height:      height,
	}, nil
}

// 🔧 Health check for Gemma 3 service
func (s *Gemma3Service) HealthCheck(ctx context.Context) error {
	req := &OllamaRequest{
		Model:  s.config.ModelName,
		Prompt: "Hello, are you working?",
		Stream: false,
	}

	_, err := s.makeOllamaRequest(ctx, req)
	return err
}

// 🏭 Build equipment analysis prompt
func (s *Gemma3Service) buildEquipmentAnalysisPrompt(req *HVACEquipmentAnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString("# HVAC Equipment Specification Extraction\n\n")
	prompt.WriteString("You are an expert HVAC equipment analyst with deep knowledge of manufacturer specifications.\n")
	prompt.WriteString("Extract detailed equipment specifications from the provided content.\n\n")

	// Content details
	prompt.WriteString("## Source Information:\n")
	prompt.WriteString(fmt.Sprintf("**URL:** %s\n", req.URL))
	prompt.WriteString(fmt.Sprintf("**Title:** %s\n", req.Title))
	prompt.WriteString(fmt.Sprintf("**Content:**\n%s\n\n", req.Content))

	// Images
	if len(req.Images) > 0 {
		prompt.WriteString("## Product Images:\n")
		for _, img := range req.Images {
			prompt.WriteString(fmt.Sprintf("**%s** - %s\n", img.Filename, img.Description))
		}
		prompt.WriteString("\n")
	}

	// Analysis instructions
	prompt.WriteString("## Extraction Required:\n")
	prompt.WriteString("Provide a comprehensive JSON response with the following structure:\n\n")
	prompt.WriteString("```json\n")
	prompt.WriteString("{\n")
	prompt.WriteString("  \"equipment_specs\": {\n")
	prompt.WriteString("    \"model_number\": \"exact model number\",\n")
	prompt.WriteString("    \"product_name\": \"full product name\",\n")
	prompt.WriteString("    \"product_family\": \"product line/family\",\n")
	prompt.WriteString("    \"category\": \"air_conditioner|heat_pump|furnace|boiler|etc\",\n")
	prompt.WriteString("    \"sub_category\": \"specific type\",\n")
	prompt.WriteString("    \"manufacturer\": \"manufacturer name\",\n")
	prompt.WriteString("    \"cooling_capacity\": \"BTU/h value\",\n")
	prompt.WriteString("    \"heating_capacity\": \"BTU/h value\",\n")
	prompt.WriteString("    \"seer\": \"SEER rating\",\n")
	prompt.WriteString("    \"eer\": \"EER rating\",\n")
	prompt.WriteString("    \"hspf\": \"HSPF rating\",\n")
	prompt.WriteString("    \"cop\": \"COP rating\",\n")
	prompt.WriteString("    \"features\": [\"feature1\", \"feature2\"],\n")
	prompt.WriteString("    \"applications\": [\"residential\", \"commercial\"]\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"technical_data\": {\n")
	prompt.WriteString("    \"dimensions\": {\"length\": \"value\", \"width\": \"value\", \"height\": \"value\"},\n")
	prompt.WriteString("    \"weight\": \"weight in lbs\",\n")
	prompt.WriteString("    \"electrical_data\": {\"voltage\": \"value\", \"amperage\": \"value\", \"phase\": \"value\"},\n")
	prompt.WriteString("    \"refrigerant_type\": \"R-410A|R-32|etc\",\n")
	prompt.WriteString("    \"refrigerant_charge\": \"charge amount\",\n")
	prompt.WriteString("    \"operating_range\": {\"min_temp\": \"value\", \"max_temp\": \"value\"},\n")
	prompt.WriteString("    \"noise_level\": \"dB rating\",\n")
	prompt.WriteString("    \"airflow_rating\": \"CFM value\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"compliance_data\": {\n")
	prompt.WriteString("    \"ahri_number\": \"AHRI certification number\",\n")
	prompt.WriteString("    \"energy_star\": true/false,\n")
	prompt.WriteString("    \"certifications\": [\"cert1\", \"cert2\"],\n")
	prompt.WriteString("    \"standards\": [\"standard1\", \"standard2\"],\n")
	prompt.WriteString("    \"warranty_info\": \"warranty details\",\n")
	prompt.WriteString("    \"installation_requirements\": [\"req1\", \"req2\"]\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"data_quality\": {\n")
	prompt.WriteString("    \"overall_score\": 0.0-1.0,\n")
	prompt.WriteString("    \"completeness_score\": 0.0-1.0,\n")
	prompt.WriteString("    \"accuracy_score\": 0.0-1.0,\n")
	prompt.WriteString("    \"missing_fields\": [\"field1\", \"field2\"],\n")
	prompt.WriteString("    \"confidence_by_field\": {\"field\": 0.0-1.0},\n")
	prompt.WriteString("    \"data_sources\": [\"manufacturer_spec\", \"product_page\"]\n")
	prompt.WriteString("  }\n")
	prompt.WriteString("}\n")
	prompt.WriteString("```\n\n")

	prompt.WriteString("Focus on extracting accurate technical specifications, efficiency ratings, and compliance information.\n")
	prompt.WriteString("If images are provided, analyze them for technical diagrams, specification tables, or product details.\n")
	prompt.WriteString("Provide confidence scores for extracted data quality.\n")

	return prompt.String()
}

// 📊 Parse equipment analysis response
func (s *Gemma3Service) parseEquipmentAnalysisResponse(response string) (*HVACEquipmentAnalysisResponse, error) {
	// Extract JSON from response (handle markdown code blocks)
	jsonStart := strings.Index(response, "{")
	jsonEnd := strings.LastIndex(response, "}")

	if jsonStart == -1 || jsonEnd == -1 {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonStr := response[jsonStart : jsonEnd+1]

	var analysisResp HVACEquipmentAnalysisResponse
	if err := json.Unmarshal([]byte(jsonStr), &analysisResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON response: %w", err)
	}

	// Set default confidence score if not provided
	if analysisResp.ConfidenceScore == 0 {
		analysisResp.ConfidenceScore = 0.8
	}

	return &analysisResp, nil
}
