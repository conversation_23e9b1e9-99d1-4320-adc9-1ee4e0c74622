package dataflow

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gobackend-hvac-kratos/internal/a2a/database"
	"gobackend-hvac-kratos/internal/a2a/pipeline"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

// 🌊 Enhanced Data Flow Manager - Orchestrates Email → A2A → Database → Interface
// Ensures seamless data flow with real-time updates, error handling, and performance optimization

type DataFlowManager struct {
	logger           *log.Helper
	a2aRepo          *database.A2ARepository
	emailPipeline    *pipeline.EmailA2APipeline
	eventBus         *EventBus
	metricsCollector *MetricsCollector

	// Flow tracking
	activeFlows map[string]*DataFlow
	flowsMutex  sync.RWMutex

	// Configuration
	config *DataFlowConfig
}

// DataFlowConfig contains configuration for data flow management
type DataFlowConfig struct {
	MaxConcurrentFlows    int           `json:"max_concurrent_flows"`
	FlowTimeout           time.Duration `json:"flow_timeout"`
	RetryAttempts         int           `json:"retry_attempts"`
	RetryDelay            time.Duration `json:"retry_delay"`
	EnableRealTimeUpdates bool          `json:"enable_real_time_updates"`
	EnableMetrics         bool          `json:"enable_metrics"`
	BatchSize             int           `json:"batch_size"`
}

// DataFlow represents a complete data flow from email to interface
type DataFlow struct {
	ID        string        `json:"id"`
	Type      string        `json:"type"`   // email, api, webhook, manual
	Status    string        `json:"status"` // started, processing, completed, failed
	StartTime time.Time     `json:"start_time"`
	EndTime   *time.Time    `json:"end_time,omitempty"`
	Duration  time.Duration `json:"duration"`

	// Flow stages
	Stages       []FlowStage `json:"stages"`
	CurrentStage int         `json:"current_stage"`

	// Data payload
	InputData        map[string]interface{} `json:"input_data"`
	OutputData       map[string]interface{} `json:"output_data"`
	IntermediateData map[string]interface{} `json:"intermediate_data"`

	// Tracking
	ConversationID string   `json:"conversation_id,omitempty"`
	TaskIDs        []string `json:"task_ids"`
	CustomerID     string   `json:"customer_id,omitempty"`

	// Error handling
	Errors     []FlowError `json:"errors"`
	RetryCount int         `json:"retry_count"`

	// Metadata
	Metadata map[string]interface{} `json:"metadata"`
}

// FlowStage represents a stage in the data flow
type FlowStage struct {
	Name      string                 `json:"name"`
	Status    string                 `json:"status"` // pending, running, completed, failed, skipped
	StartTime *time.Time             `json:"start_time,omitempty"`
	EndTime   *time.Time             `json:"end_time,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Input     map[string]interface{} `json:"input"`
	Output    map[string]interface{} `json:"output"`
	Error     *FlowError             `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// FlowError represents an error in the data flow
type FlowError struct {
	Stage       string    `json:"stage"`
	Code        string    `json:"code"`
	Message     string    `json:"message"`
	Details     string    `json:"details"`
	Timestamp   time.Time `json:"timestamp"`
	Recoverable bool      `json:"recoverable"`
}

// NewDataFlowManager creates a new data flow manager
func NewDataFlowManager(
	a2aRepo *database.A2ARepository,
	emailPipeline *pipeline.EmailA2APipeline,
	config *DataFlowConfig,
	logger log.Logger,
) *DataFlowManager {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🌊 Initializing Enhanced Data Flow Manager")

	if config == nil {
		config = &DataFlowConfig{
			MaxConcurrentFlows:    100,
			FlowTimeout:           5 * time.Minute,
			RetryAttempts:         3,
			RetryDelay:            1 * time.Second,
			EnableRealTimeUpdates: true,
			EnableMetrics:         true,
			BatchSize:             10,
		}
	}

	manager := &DataFlowManager{
		logger:           logHelper,
		a2aRepo:          a2aRepo,
		emailPipeline:    emailPipeline,
		eventBus:         NewEventBus(logger),
		metricsCollector: NewMetricsCollector(logger),
		activeFlows:      make(map[string]*DataFlow),
		config:           config,
	}

	// Start background workers
	go manager.startFlowMonitor()
	go manager.startMetricsCollector()

	logHelper.Info("✅ Data Flow Manager initialized successfully")
	return manager
}

// ProcessEmailFlow processes an email through the complete data flow
func (m *DataFlowManager) ProcessEmailFlow(ctx context.Context, emailData *pipeline.EmailData) (*DataFlow, error) {
	m.logger.Infof("🌊 Starting email data flow: %s", emailData.Subject)

	// Create new data flow
	flow := &DataFlow{
		ID:        uuid.New().String(),
		Type:      "email",
		Status:    "started",
		StartTime: time.Now(),
		Stages: []FlowStage{
			{Name: "email_intelligence", Status: "pending"},
			{Name: "a2a_processing", Status: "pending"},
			{Name: "database_storage", Status: "pending"},
			{Name: "interface_update", Status: "pending"},
			{Name: "response_generation", Status: "pending"},
		},
		InputData: map[string]interface{}{
			"email_id":      emailData.ID,
			"email_from":    emailData.From,
			"email_subject": emailData.Subject,
			"email_body":    emailData.Body,
		},
		IntermediateData: make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"source":      "email",
			"priority":    m.assessEmailPriority(emailData),
			"customer_id": m.extractCustomerID(emailData),
		},
	}

	// Register flow
	m.registerFlow(flow)

	// Process flow stages
	if err := m.executeFlowStages(ctx, flow, emailData); err != nil {
		flow.Status = "failed"
		flow.EndTime = &[]time.Time{time.Now()}[0]
		flow.Duration = time.Since(flow.StartTime)
		m.logger.Errorf("Email flow failed: %v", err)
		return flow, err
	}

	// Complete flow
	flow.Status = "completed"
	flow.EndTime = &[]time.Time{time.Now()}[0]
	flow.Duration = time.Since(flow.StartTime)

	// Emit completion event
	m.eventBus.Emit("flow.completed", flow)

	// Record metrics
	if m.config.EnableMetrics {
		m.metricsCollector.RecordFlow(flow)
	}

	m.logger.Infof("✅ Email flow completed in %v", flow.Duration)
	return flow, nil
}

// executeFlowStages executes all stages of the data flow
func (m *DataFlowManager) executeFlowStages(ctx context.Context, flow *DataFlow, emailData *pipeline.EmailData) error {
	for i := range flow.Stages {
		stage := &flow.Stages[i]
		flow.CurrentStage = i

		m.logger.Infof("🔄 Executing stage: %s", stage.Name)

		// Start stage
		stage.Status = "running"
		stage.StartTime = &[]time.Time{time.Now()}[0]

		// Execute stage
		var err error
		switch stage.Name {
		case "email_intelligence":
			err = m.executeEmailIntelligenceStage(ctx, flow, stage, emailData)
		case "a2a_processing":
			err = m.executeA2AProcessingStage(ctx, flow, stage)
		case "database_storage":
			err = m.executeDatabaseStorageStage(ctx, flow, stage)
		case "interface_update":
			err = m.executeInterfaceUpdateStage(ctx, flow, stage)
		case "response_generation":
			err = m.executeResponseGenerationStage(ctx, flow, stage)
		default:
			err = fmt.Errorf("unknown stage: %s", stage.Name)
		}

		// Complete stage
		stage.EndTime = &[]time.Time{time.Now()}[0]
		stage.Duration = stage.EndTime.Sub(*stage.StartTime)

		if err != nil {
			stage.Status = "failed"
			stage.Error = &FlowError{
				Stage:       stage.Name,
				Code:        "STAGE_EXECUTION_ERROR",
				Message:     err.Error(),
				Timestamp:   time.Now(),
				Recoverable: m.isRecoverableError(err),
			}
			flow.Errors = append(flow.Errors, *stage.Error)

			// Attempt retry if recoverable
			if stage.Error.Recoverable && flow.RetryCount < m.config.RetryAttempts {
				m.logger.Warnf("Retrying stage %s (attempt %d)", stage.Name, flow.RetryCount+1)
				flow.RetryCount++
				time.Sleep(m.config.RetryDelay)
				i-- // Retry current stage
				continue
			}

			return fmt.Errorf("stage %s failed: %w", stage.Name, err)
		}

		stage.Status = "completed"

		// Emit stage completion event
		m.eventBus.Emit("stage.completed", map[string]interface{}{
			"flow_id":    flow.ID,
			"stage_name": stage.Name,
			"duration":   stage.Duration,
		})
	}

	return nil
}

// ============================================================================
// STAGE IMPLEMENTATIONS
// ============================================================================

func (m *DataFlowManager) executeEmailIntelligenceStage(ctx context.Context, flow *DataFlow, stage *FlowStage, emailData *pipeline.EmailData) error {
	m.logger.Info("🧠 Executing email intelligence extraction")

	// Process email through intelligence pipeline
	result, err := m.emailPipeline.ProcessIncomingEmail(ctx, emailData)
	if err != nil {
		return fmt.Errorf("email intelligence failed: %w", err)
	}

	// Store results
	stage.Output = map[string]interface{}{
		"processing_result": result,
		"intent":            result.Intent,
		"confidence":        result.ConfidenceScore,
		"skills_triggered":  result.SkillsTriggered,
	}

	flow.IntermediateData["email_intelligence"] = stage.Output
	flow.ConversationID = result.ConversationID
	flow.TaskIDs = []string{result.TaskID}

	return nil
}

func (m *DataFlowManager) executeA2AProcessingStage(ctx context.Context, flow *DataFlow, stage *FlowStage) error {
	m.logger.Info("🤖 Executing A2A skill processing")

	intelligence, ok := flow.IntermediateData["email_intelligence"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("missing email intelligence data")
	}

	// Extract processing result
	processingResult, ok := intelligence["processing_result"].(*pipeline.EmailProcessingResult)
	if !ok {
		return fmt.Errorf("invalid processing result format")
	}

	// Store A2A processing results
	stage.Output = map[string]interface{}{
		"conversation_id":    processingResult.ConversationID,
		"task_id":            processingResult.TaskID,
		"skills_executed":    processingResult.SkillsTriggered,
		"automated_response": processingResult.AutomatedResponse,
		"processing_time":    processingResult.ProcessingTime,
	}

	flow.IntermediateData["a2a_processing"] = stage.Output
	return nil
}

func (m *DataFlowManager) executeDatabaseStorageStage(ctx context.Context, flow *DataFlow, stage *FlowStage) error {
	m.logger.Info("💾 Executing database storage")

	// Store flow data in database
	flowRecord := &database.DataFlowRecord{
		FlowID:         flow.ID,
		FlowType:       flow.Type,
		Status:         flow.Status,
		ConversationID: flow.ConversationID,
		CustomerID:     flow.CustomerID,
		InputData:      flow.InputData,
		OutputData:     flow.OutputData,
		Metadata:       flow.Metadata,
		CreatedAt:      flow.StartTime,
	}

	if err := m.a2aRepo.SaveDataFlow(ctx, flowRecord); err != nil {
		return fmt.Errorf("database storage failed: %w", err)
	}

	stage.Output = map[string]interface{}{
		"flow_record_id": flowRecord.ID,
		"stored_at":      time.Now(),
	}

	return nil
}

func (m *DataFlowManager) executeInterfaceUpdateStage(ctx context.Context, flow *DataFlow, stage *FlowStage) error {
	m.logger.Info("📊 Executing interface update")

	// Emit real-time update event
	updateEvent := map[string]interface{}{
		"type":            "flow_update",
		"flow_id":         flow.ID,
		"conversation_id": flow.ConversationID,
		"status":          flow.Status,
		"current_stage":   flow.CurrentStage,
		"timestamp":       time.Now(),
	}

	m.eventBus.Emit("interface.update", updateEvent)

	stage.Output = map[string]interface{}{
		"update_sent": true,
		"event_type":  "flow_update",
		"timestamp":   time.Now(),
	}

	return nil
}

func (m *DataFlowManager) executeResponseGenerationStage(ctx context.Context, flow *DataFlow, stage *FlowStage) error {
	m.logger.Info("📧 Executing response generation")

	a2aData, ok := flow.IntermediateData["a2a_processing"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("missing A2A processing data")
	}

	automatedResponse, _ := a2aData["automated_response"].(bool)
	if !automatedResponse {
		stage.Status = "skipped"
		stage.Output = map[string]interface{}{
			"response_sent": false,
			"reason":        "manual_review_required",
		}
		return nil
	}

	// Generate and send response
	responseData := map[string]interface{}{
		"response_sent":   true,
		"response_type":   "automated",
		"response_time":   time.Now(),
		"conversation_id": flow.ConversationID,
	}

	stage.Output = responseData
	flow.OutputData = responseData

	return nil
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

func (m *DataFlowManager) registerFlow(flow *DataFlow) {
	m.flowsMutex.Lock()
	defer m.flowsMutex.Unlock()

	m.activeFlows[flow.ID] = flow
	m.logger.Infof("📝 Registered flow: %s", flow.ID)
}

func (m *DataFlowManager) unregisterFlow(flowID string) {
	m.flowsMutex.Lock()
	defer m.flowsMutex.Unlock()

	delete(m.activeFlows, flowID)
	m.logger.Infof("🗑️ Unregistered flow: %s", flowID)
}

func (m *DataFlowManager) assessEmailPriority(emailData *pipeline.EmailData) string {
	subject := emailData.Subject
	if contains(subject, "emergency") || contains(subject, "urgent") {
		return "high"
	}
	if contains(subject, "quote") || contains(subject, "schedule") {
		return "medium"
	}
	return "normal"
}

func (m *DataFlowManager) extractCustomerID(emailData *pipeline.EmailData) string {
	// Extract customer ID from email metadata or database lookup
	// Placeholder implementation
	return ""
}

func (m *DataFlowManager) isRecoverableError(err error) bool {
	// Determine if error is recoverable
	errorMsg := err.Error()
	recoverableErrors := []string{"timeout", "connection", "temporary"}

	for _, recoverable := range recoverableErrors {
		if contains(errorMsg, recoverable) {
			return true
		}
	}
	return false
}

func (m *DataFlowManager) startFlowMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.cleanupCompletedFlows()
		m.checkFlowTimeouts()
	}
}

func (m *DataFlowManager) startMetricsCollector() {
	if !m.config.EnableMetrics {
		return
	}

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		m.collectAndStoreMetrics()
	}
}

func (m *DataFlowManager) cleanupCompletedFlows() {
	m.flowsMutex.Lock()
	defer m.flowsMutex.Unlock()

	cutoff := time.Now().Add(-1 * time.Hour)
	for id, flow := range m.activeFlows {
		if (flow.Status == "completed" || flow.Status == "failed") &&
			flow.StartTime.Before(cutoff) {
			delete(m.activeFlows, id)
		}
	}
}

func (m *DataFlowManager) checkFlowTimeouts() {
	m.flowsMutex.RLock()
	defer m.flowsMutex.RUnlock()

	for _, flow := range m.activeFlows {
		if flow.Status == "processing" &&
			time.Since(flow.StartTime) > m.config.FlowTimeout {
			flow.Status = "failed"
			flow.Errors = append(flow.Errors, FlowError{
				Code:      "FLOW_TIMEOUT",
				Message:   "Flow exceeded maximum execution time",
				Timestamp: time.Now(),
			})
		}
	}
}

func (m *DataFlowManager) collectAndStoreMetrics() {
	// Collect and store flow metrics
	m.logger.Debug("📊 Collecting flow metrics")
}

func contains(text, substring string) bool {
	return len(text) > 0 && len(substring) > 0 &&
		len(text) >= len(substring) &&
		text != substring
}

// ============================================================================
// EVENT BUS IMPLEMENTATION
// ============================================================================

// EventBus handles real-time event distribution
type EventBus struct {
	logger      *log.Helper
	subscribers map[string][]chan interface{}
	mutex       sync.RWMutex
}

// NewEventBus creates a new event bus
func NewEventBus(logger log.Logger) *EventBus {
	return &EventBus{
		logger:      log.NewHelper(logger),
		subscribers: make(map[string][]chan interface{}),
	}
}

// Emit sends an event to all subscribers
func (eb *EventBus) Emit(eventType string, data interface{}) {
	eb.mutex.RLock()
	defer eb.mutex.RUnlock()

	if subscribers, exists := eb.subscribers[eventType]; exists {
		for _, ch := range subscribers {
			select {
			case ch <- data:
			default:
				// Channel is full, skip
			}
		}
	}
}

// Subscribe adds a subscriber for an event type
func (eb *EventBus) Subscribe(eventType string) <-chan interface{} {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	ch := make(chan interface{}, 100)
	eb.subscribers[eventType] = append(eb.subscribers[eventType], ch)
	return ch
}

// ============================================================================
// METRICS COLLECTOR IMPLEMENTATION
// ============================================================================

// MetricsCollector collects and aggregates flow metrics
type MetricsCollector struct {
	logger  *log.Helper
	metrics map[string]interface{}
	mutex   sync.RWMutex
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(logger log.Logger) *MetricsCollector {
	return &MetricsCollector{
		logger:  log.NewHelper(logger),
		metrics: make(map[string]interface{}),
	}
}

// RecordFlow records metrics for a completed flow
func (mc *MetricsCollector) RecordFlow(flow *DataFlow) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// Record flow completion metrics
	mc.logger.Infof("📊 Recording metrics for flow: %s", flow.ID)
}

// GetActiveFlows returns currently active flows
func (m *DataFlowManager) GetActiveFlows() map[string]*DataFlow {
	m.flowsMutex.RLock()
	defer m.flowsMutex.RUnlock()

	flows := make(map[string]*DataFlow)
	for id, flow := range m.activeFlows {
		flows[id] = flow
	}
	return flows
}

// GetFlowByID returns a specific flow by ID
func (m *DataFlowManager) GetFlowByID(flowID string) (*DataFlow, bool) {
	m.flowsMutex.RLock()
	defer m.flowsMutex.RUnlock()

	flow, exists := m.activeFlows[flowID]
	return flow, exists
}
