package database

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gobackend-hvac-kratos/internal/a2a"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 🗄️ A2A Database Repository - Persistent storage for A2A protocol
// Integrates A2A conversations, tasks, and metrics with PostgreSQL

// A2ARepository handles all A2A database operations
type A2ARepository struct {
	db     *gorm.DB
	logger *log.Helper
}

// NewA2ARepository creates a new A2A database repository
func NewA2ARepository(db *gorm.DB, logger log.Logger) *A2ARepository {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🗄️ Initializing A2A Database Repository")

	return &A2ARepository{
		db:     db,
		logger: logHelper,
	}
}

// ============================================================================
// CONVERSATION MANAGEMENT
// ============================================================================

// A2AConversation represents a persistent conversation context
type A2AConversation struct {
	ID         int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	ContextID  string                 `gorm:"uniqueIndex;not null" json:"context_id"`
	CustomerID *int64                 `gorm:"index" json:"customer_id,omitempty"`
	AgentName  string                 `gorm:"not null;default:'HVAC CRM Intelligence Agent'" json:"agent_name"`
	Status     string                 `gorm:"not null;default:'active'" json:"status"`
	CreatedAt  time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	ExpiresAt  *time.Time             `json:"expires_at,omitempty"`
	Metadata   map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	Tasks    []A2ATask    `gorm:"foreignKey:ConversationID" json:"tasks,omitempty"`
	Messages []A2AMessage `gorm:"foreignKey:ConversationID" json:"messages,omitempty"`
}

// CreateConversation creates a new A2A conversation
func (r *A2ARepository) CreateConversation(ctx context.Context, contextID string, customerID *int64) (*A2AConversation, error) {
	r.logger.Infof("💬 Creating A2A conversation: %s", contextID)

	conversation := &A2AConversation{
		ContextID:  contextID,
		CustomerID: customerID,
		Status:     "active",
		Metadata:   make(map[string]interface{}),
	}

	if err := r.db.WithContext(ctx).Create(conversation).Error; err != nil {
		r.logger.Errorf("Failed to create conversation: %v", err)
		return nil, err
	}

	r.logger.Infof("✅ Created conversation ID: %d", conversation.ID)
	return conversation, nil
}

// GetConversation retrieves a conversation by context ID
func (r *A2ARepository) GetConversation(ctx context.Context, contextID string) (*A2AConversation, error) {
	var conversation A2AConversation

	err := r.db.WithContext(ctx).
		Preload("Tasks").
		Preload("Messages").
		Where("context_id = ?", contextID).
		First(&conversation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // Not found, not an error
		}
		return nil, err
	}

	return &conversation, nil
}

// UpdateConversationStatus updates conversation status
func (r *A2ARepository) UpdateConversationStatus(ctx context.Context, contextID string, status string) error {
	return r.db.WithContext(ctx).
		Model(&A2AConversation{}).
		Where("context_id = ?", contextID).
		Update("status", status).Error
}

// ============================================================================
// TASK MANAGEMENT
// ============================================================================

// A2ATask represents a persistent A2A task
type A2ATask struct {
	ID             int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	TaskID         string                 `gorm:"uniqueIndex;not null" json:"task_id"`
	ConversationID int64                  `gorm:"index" json:"conversation_id"`
	SkillName      string                 `gorm:"not null" json:"skill_name"`
	Status         string                 `gorm:"not null;default:'submitted'" json:"status"`
	Priority       string                 `gorm:"default:'medium'" json:"priority"`
	InputData      map[string]interface{} `gorm:"type:jsonb" json:"input_data"`
	OutputData     map[string]interface{} `gorm:"type:jsonb" json:"output_data"`
	ErrorMessage   *string                `json:"error_message,omitempty"`
	StartedAt      *time.Time             `json:"started_at,omitempty"`
	CompletedAt    *time.Time             `json:"completed_at,omitempty"`
	CreatedAt      time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	Metadata       map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	Conversation    A2AConversation     `gorm:"foreignKey:ConversationID" json:"conversation,omitempty"`
	Messages        []A2AMessage        `gorm:"foreignKey:TaskID" json:"messages,omitempty"`
	SkillExecutions []A2ASkillExecution `gorm:"foreignKey:TaskID" json:"skill_executions,omitempty"`
}

// CreateTask creates a new A2A task
func (r *A2ARepository) CreateTask(ctx context.Context, task *a2a.Task, conversationID int64) (*A2ATask, error) {
	r.logger.Infof("📋 Creating A2A task: %s", task.ID)

	dbTask := &A2ATask{
		TaskID:         task.ID,
		ConversationID: conversationID,
		SkillName:      extractSkillFromTask(task),
		Status:         string(task.Status.State),
		InputData:      extractInputFromTask(task),
		Metadata:       task.Metadata,
	}

	if err := r.db.WithContext(ctx).Create(dbTask).Error; err != nil {
		r.logger.Errorf("Failed to create task: %v", err)
		return nil, err
	}

	r.logger.Infof("✅ Created task ID: %d", dbTask.ID)
	return dbTask, nil
}

// UpdateTaskStatus updates task status and completion info
func (r *A2ARepository) UpdateTaskStatus(ctx context.Context, taskID string, status a2a.TaskState, outputData map[string]interface{}, errorMsg *string) error {
	updates := map[string]interface{}{
		"status":     string(status),
		"updated_at": time.Now(),
	}

	if outputData != nil {
		updates["output_data"] = outputData
	}

	if errorMsg != nil {
		updates["error_message"] = *errorMsg
	}

	if status == a2a.TaskStateWorking && updates["started_at"] == nil {
		updates["started_at"] = time.Now()
	}

	if status == a2a.TaskStateCompleted || status == a2a.TaskStateFailed || status == a2a.TaskStateCanceled {
		updates["completed_at"] = time.Now()
	}

	return r.db.WithContext(ctx).
		Model(&A2ATask{}).
		Where("task_id = ?", taskID).
		Updates(updates).Error
}

// GetTask retrieves a task by task ID
func (r *A2ARepository) GetTask(ctx context.Context, taskID string) (*A2ATask, error) {
	var task A2ATask

	err := r.db.WithContext(ctx).
		Preload("Conversation").
		Preload("Messages").
		Preload("SkillExecutions").
		Where("task_id = ?", taskID).
		First(&task).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &task, nil
}

// ============================================================================
// MESSAGE MANAGEMENT
// ============================================================================

// A2AMessage represents a persistent A2A message
type A2AMessage struct {
	ID             int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	MessageID      string                 `gorm:"uniqueIndex;not null" json:"message_id"`
	ConversationID int64                  `gorm:"index" json:"conversation_id"`
	TaskID         *int64                 `gorm:"index" json:"task_id,omitempty"`
	Role           string                 `gorm:"not null" json:"role"`
	Content        map[string]interface{} `gorm:"type:jsonb" json:"content"`
	TokenUsage     map[string]interface{} `gorm:"type:jsonb" json:"token_usage,omitempty"`
	CreatedAt      time.Time              `gorm:"autoCreateTime" json:"created_at"`
	Metadata       map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	Conversation A2AConversation `gorm:"foreignKey:ConversationID" json:"conversation,omitempty"`
	Task         *A2ATask        `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// SaveMessage saves an A2A message to database
func (r *A2ARepository) SaveMessage(ctx context.Context, message *a2a.Message, conversationID int64, taskID *int64) (*A2AMessage, error) {
	r.logger.Infof("💬 Saving A2A message: %s", message.MessageID)

	// Convert message parts to JSON
	contentJSON, err := json.Marshal(message.Parts)
	if err != nil {
		return nil, err
	}

	var content map[string]interface{}
	if err := json.Unmarshal(contentJSON, &content); err != nil {
		return nil, err
	}

	dbMessage := &A2AMessage{
		MessageID:      message.MessageID,
		ConversationID: conversationID,
		TaskID:         taskID,
		Role:           message.Role,
		Content:        content,
		Metadata:       message.Metadata,
	}

	if err := r.db.WithContext(ctx).Create(dbMessage).Error; err != nil {
		r.logger.Errorf("Failed to save message: %v", err)
		return nil, err
	}

	return dbMessage, nil
}

// GetConversationMessages retrieves all messages for a conversation
func (r *A2ARepository) GetConversationMessages(ctx context.Context, contextID string, limit int) ([]A2AMessage, error) {
	var messages []A2AMessage

	query := r.db.WithContext(ctx).
		Joins("JOIN a2a_conversations ON a2a_messages.conversation_id = a2a_conversations.id").
		Where("a2a_conversations.context_id = ?", contextID).
		Order("a2a_messages.created_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&messages).Error
	return messages, err
}

// ============================================================================
// SKILL EXECUTION TRACKING
// ============================================================================

// A2ASkillExecution represents a skill execution record
type A2ASkillExecution struct {
	ID              int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	ExecutionID     string                 `gorm:"uniqueIndex;not null" json:"execution_id"`
	TaskID          int64                  `gorm:"index" json:"task_id"`
	SkillName       string                 `gorm:"not null" json:"skill_name"`
	ExecutionType   string                 `gorm:"not null" json:"execution_type"`
	InputSize       int                    `gorm:"default:0" json:"input_size"`
	OutputSize      int                    `gorm:"default:0" json:"output_size"`
	ExecutionTimeMs int                    `gorm:"default:0" json:"execution_time_ms"`
	Success         bool                   `gorm:"default:false" json:"success"`
	ErrorCode       *string                `json:"error_code,omitempty"`
	ErrorMessage    *string                `json:"error_message,omitempty"`
	BridgeTool      *string                `json:"bridge_tool,omitempty"`
	CreatedAt       time.Time              `gorm:"autoCreateTime" json:"created_at"`
	Metadata        map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	Task A2ATask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// RecordSkillExecution records a skill execution
func (r *A2ARepository) RecordSkillExecution(ctx context.Context, execution *SkillExecutionRecord) error {
	r.logger.Infof("📊 Recording skill execution: %s", execution.SkillName)

	dbExecution := &A2ASkillExecution{
		ExecutionID:     execution.ExecutionID,
		TaskID:          execution.TaskID,
		SkillName:       execution.SkillName,
		ExecutionType:   execution.ExecutionType,
		InputSize:       execution.InputSize,
		OutputSize:      execution.OutputSize,
		ExecutionTimeMs: execution.ExecutionTimeMs,
		Success:         execution.Success,
		ErrorCode:       execution.ErrorCode,
		ErrorMessage:    execution.ErrorMessage,
		BridgeTool:      execution.BridgeTool,
		Metadata:        execution.Metadata,
	}

	return r.db.WithContext(ctx).Create(dbExecution).Error
}

// ============================================================================
// DATA FLOW TRACKING
// ============================================================================

// DataFlowRecord represents a complete data flow record
type DataFlowRecord struct {
	ID             int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	FlowID         string                 `gorm:"uniqueIndex;not null" json:"flow_id"`
	FlowType       string                 `gorm:"not null" json:"flow_type"`
	Status         string                 `gorm:"not null" json:"status"`
	ConversationID string                 `gorm:"index" json:"conversation_id,omitempty"`
	CustomerID     string                 `gorm:"index" json:"customer_id,omitempty"`
	InputData      map[string]interface{} `gorm:"type:jsonb" json:"input_data"`
	OutputData     map[string]interface{} `gorm:"type:jsonb" json:"output_data"`
	Metadata       map[string]interface{} `gorm:"type:jsonb" json:"metadata"`
	CreatedAt      time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
}

// SaveDataFlow saves a data flow record to database
func (r *A2ARepository) SaveDataFlow(ctx context.Context, flowRecord *DataFlowRecord) error {
	r.logger.Infof("💾 Saving data flow record: %s", flowRecord.FlowID)

	if err := r.db.WithContext(ctx).Create(flowRecord).Error; err != nil {
		r.logger.Errorf("Failed to save data flow: %v", err)
		return err
	}

	r.logger.Infof("✅ Data flow saved with ID: %d", flowRecord.ID)
	return nil
}

// ============================================================================
// HELPER TYPES AND FUNCTIONS
// ============================================================================

// SkillExecutionRecord represents skill execution data
type SkillExecutionRecord struct {
	ExecutionID     string
	TaskID          int64
	SkillName       string
	ExecutionType   string
	InputSize       int
	OutputSize      int
	ExecutionTimeMs int
	Success         bool
	ErrorCode       *string
	ErrorMessage    *string
	BridgeTool      *string
	Metadata        map[string]interface{}
}

// extractSkillFromTask extracts skill name from task metadata
func extractSkillFromTask(task *a2a.Task) string {
	if skill, ok := task.Metadata["skill_name"].(string); ok {
		return skill
	}
	return "unknown"
}

// extractInputFromTask extracts input data from task
func extractInputFromTask(task *a2a.Task) map[string]interface{} {
	if len(task.History) > 0 {
		// Convert first message to input data
		message := task.History[0]
		input := make(map[string]interface{})

		for i, part := range message.Parts {
			if part.Type == "text" {
				input[fmt.Sprintf("text_%d", i)] = part.Text
			} else if part.Type == "data" {
				input[fmt.Sprintf("data_%d", i)] = part.Data
			}
		}

		return input
	}
	return make(map[string]interface{})
}
