package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"gobackend-hvac-kratos/internal/a2a/database"
	"gobackend-hvac-kratos/internal/a2a/dataflow"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
)

// 🔄 Real-time Data Synchronizer - Ensures data consistency across Email → A2A → Database → Interface
// Provides real-time updates, conflict resolution, and data integrity

type RealTimeSynchronizer struct {
	logger           *log.Helper
	a2aRepo          *database.A2ARepository
	dataFlowManager  *dataflow.DataFlowManager
	
	// WebSocket connections for real-time updates
	wsConnections    map[string]*WebSocketConnection
	connectionsMutex sync.RWMutex
	
	// Data synchronization
	syncQueue        chan *SyncEvent
	syncWorkers      int
	
	// Configuration
	config           *SyncConfig
}

// SyncConfig contains synchronization configuration
type SyncConfig struct {
	MaxConnections     int           `json:"max_connections"`
	SyncWorkers        int           `json:"sync_workers"`
	QueueSize          int           `json:"queue_size"`
	HeartbeatInterval  time.Duration `json:"heartbeat_interval"`
	ReconnectAttempts  int           `json:"reconnect_attempts"`
	EnableCompression  bool          `json:"enable_compression"`
	EnableBatching     bool          `json:"enable_batching"`
	BatchSize          int           `json:"batch_size"`
	BatchTimeout       time.Duration `json:"batch_timeout"`
}

// WebSocketConnection represents a WebSocket connection with metadata
type WebSocketConnection struct {
	ID           string                 `json:"id"`
	Conn         *websocket.Conn        `json:"-"`
	UserID       string                 `json:"user_id"`
	Subscriptions []string              `json:"subscriptions"`
	LastPing     time.Time              `json:"last_ping"`
	Connected    time.Time              `json:"connected"`
	Metadata     map[string]interface{} `json:"metadata"`
	SendQueue    chan []byte            `json:"-"`
	mutex        sync.Mutex             `json:"-"`
}

// SyncEvent represents a data synchronization event
type SyncEvent struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // conversation, task, message, flow, metric
	Action      string                 `json:"action"` // create, update, delete, status_change
	EntityID    string                 `json:"entity_id"`
	Data        map[string]interface{} `json:"data"`
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"` // email, a2a, database, interface
	Priority    int                    `json:"priority"` // 1=high, 2=medium, 3=low
	Subscribers []string               `json:"subscribers"` // specific connection IDs
	Metadata    map[string]interface{} `json:"metadata"`
}

// NewRealTimeSynchronizer creates a new real-time synchronizer
func NewRealTimeSynchronizer(
	a2aRepo *database.A2ARepository,
	dataFlowManager *dataflow.DataFlowManager,
	config *SyncConfig,
	logger log.Logger,
) *RealTimeSynchronizer {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🔄 Initializing Real-time Data Synchronizer")

	if config == nil {
		config = &SyncConfig{
			MaxConnections:    1000,
			SyncWorkers:       5,
			QueueSize:         10000,
			HeartbeatInterval: 30 * time.Second,
			ReconnectAttempts: 3,
			EnableCompression: true,
			EnableBatching:    true,
			BatchSize:         10,
			BatchTimeout:      100 * time.Millisecond,
		}
	}

	sync := &RealTimeSynchronizer{
		logger:          logHelper,
		a2aRepo:         a2aRepo,
		dataFlowManager: dataFlowManager,
		wsConnections:   make(map[string]*WebSocketConnection),
		syncQueue:       make(chan *SyncEvent, config.QueueSize),
		syncWorkers:     config.SyncWorkers,
		config:          config,
	}

	// Start sync workers
	for i := 0; i < config.SyncWorkers; i++ {
		go sync.startSyncWorker(i)
	}

	// Start connection monitor
	go sync.startConnectionMonitor()

	// Start batch processor if enabled
	if config.EnableBatching {
		go sync.startBatchProcessor()
	}

	logHelper.Info("✅ Real-time Synchronizer initialized successfully")
	return sync
}

// ============================================================================
// WEBSOCKET CONNECTION MANAGEMENT
// ============================================================================

// AddConnection adds a new WebSocket connection
func (s *RealTimeSynchronizer) AddConnection(connID, userID string, conn *websocket.Conn) *WebSocketConnection {
	s.connectionsMutex.Lock()
	defer s.connectionsMutex.Unlock()

	s.logger.Infof("🔌 Adding WebSocket connection: %s (user: %s)", connID, userID)

	wsConn := &WebSocketConnection{
		ID:            connID,
		Conn:          conn,
		UserID:        userID,
		Subscriptions: []string{},
		LastPing:      time.Now(),
		Connected:     time.Now(),
		Metadata:      make(map[string]interface{}),
		SendQueue:     make(chan []byte, 100),
	}

	s.wsConnections[connID] = wsConn

	// Start connection handler
	go s.handleConnection(wsConn)

	// Send welcome message
	s.sendToConnection(wsConn, &SyncEvent{
		Type:   "system",
		Action: "connected",
		Data: map[string]interface{}{
			"connection_id": connID,
			"server_time":   time.Now(),
			"capabilities": []string{
				"real_time_updates",
				"batch_processing",
				"compression",
				"heartbeat",
			},
		},
		Timestamp: time.Now(),
	})

	return wsConn
}

// RemoveConnection removes a WebSocket connection
func (s *RealTimeSynchronizer) RemoveConnection(connID string) {
	s.connectionsMutex.Lock()
	defer s.connectionsMutex.Unlock()

	if conn, exists := s.wsConnections[connID]; exists {
		s.logger.Infof("🔌 Removing WebSocket connection: %s", connID)
		
		conn.Conn.Close()
		close(conn.SendQueue)
		delete(s.wsConnections, connID)
	}
}

// Subscribe adds subscriptions for a connection
func (s *RealTimeSynchronizer) Subscribe(connID string, subscriptions []string) error {
	s.connectionsMutex.Lock()
	defer s.connectionsMutex.Unlock()

	conn, exists := s.wsConnections[connID]
	if !exists {
		return fmt.Errorf("connection not found: %s", connID)
	}

	conn.mutex.Lock()
	defer conn.mutex.Unlock()

	// Add new subscriptions
	for _, sub := range subscriptions {
		if !contains(conn.Subscriptions, sub) {
			conn.Subscriptions = append(conn.Subscriptions, sub)
		}
	}

	s.logger.Infof("📡 Connection %s subscribed to: %v", connID, subscriptions)
	return nil
}

// ============================================================================
// DATA SYNCHRONIZATION
// ============================================================================

// SyncConversationUpdate synchronizes conversation updates
func (s *RealTimeSynchronizer) SyncConversationUpdate(conversationID string, data map[string]interface{}) {
	event := &SyncEvent{
		Type:      "conversation",
		Action:    "update",
		EntityID:  conversationID,
		Data:      data,
		Timestamp: time.Now(),
		Source:    "a2a",
		Priority:  2,
	}

	s.queueSyncEvent(event)
}

// SyncTaskStatusChange synchronizes task status changes
func (s *RealTimeSynchronizer) SyncTaskStatusChange(taskID string, status string, data map[string]interface{}) {
	event := &SyncEvent{
		Type:      "task",
		Action:    "status_change",
		EntityID:  taskID,
		Data: map[string]interface{}{
			"status": status,
			"data":   data,
		},
		Timestamp: time.Now(),
		Source:    "a2a",
		Priority:  1, // High priority for task updates
	}

	s.queueSyncEvent(event)
}

// SyncNewMessage synchronizes new messages
func (s *RealTimeSynchronizer) SyncNewMessage(conversationID, messageID string, message map[string]interface{}) {
	event := &SyncEvent{
		Type:      "message",
		Action:    "create",
		EntityID:  messageID,
		Data: map[string]interface{}{
			"conversation_id": conversationID,
			"message":         message,
		},
		Timestamp: time.Now(),
		Source:    "a2a",
		Priority:  1,
	}

	s.queueSyncEvent(event)
}

// SyncDataFlowUpdate synchronizes data flow updates
func (s *RealTimeSynchronizer) SyncDataFlowUpdate(flowID string, flow *dataflow.DataFlow) {
	event := &SyncEvent{
		Type:      "flow",
		Action:    "update",
		EntityID:  flowID,
		Data: map[string]interface{}{
			"flow_id":       flow.ID,
			"status":        flow.Status,
			"current_stage": flow.CurrentStage,
			"duration":      flow.Duration,
		},
		Timestamp: time.Now(),
		Source:    "dataflow",
		Priority:  2,
	}

	s.queueSyncEvent(event)
}

// SyncMetricsUpdate synchronizes metrics updates
func (s *RealTimeSynchronizer) SyncMetricsUpdate(metricType string, metrics map[string]interface{}) {
	event := &SyncEvent{
		Type:      "metric",
		Action:    "update",
		EntityID:  metricType,
		Data:      metrics,
		Timestamp: time.Now(),
		Source:    "metrics",
		Priority:  3, // Low priority for metrics
	}

	s.queueSyncEvent(event)
}

// ============================================================================
// INTERNAL PROCESSING
// ============================================================================

// queueSyncEvent adds an event to the sync queue
func (s *RealTimeSynchronizer) queueSyncEvent(event *SyncEvent) {
	select {
	case s.syncQueue <- event:
		// Event queued successfully
	default:
		s.logger.Warnf("⚠️ Sync queue full, dropping event: %s", event.Type)
	}
}

// startSyncWorker starts a sync worker goroutine
func (s *RealTimeSynchronizer) startSyncWorker(workerID int) {
	s.logger.Infof("🔄 Starting sync worker %d", workerID)

	for event := range s.syncQueue {
		s.processSyncEvent(event)
	}
}

// processSyncEvent processes a single sync event
func (s *RealTimeSynchronizer) processSyncEvent(event *SyncEvent) {
	s.logger.Debugf("🔄 Processing sync event: %s/%s", event.Type, event.Action)

	// Determine target connections
	targetConnections := s.getTargetConnections(event)

	// Send to target connections
	for _, conn := range targetConnections {
		s.sendToConnection(conn, event)
	}
}

// getTargetConnections determines which connections should receive the event
func (s *RealTimeSynchronizer) getTargetConnections(event *SyncEvent) []*WebSocketConnection {
	s.connectionsMutex.RLock()
	defer s.connectionsMutex.RUnlock()

	var targets []*WebSocketConnection

	// If specific subscribers are defined, use them
	if len(event.Subscribers) > 0 {
		for _, connID := range event.Subscribers {
			if conn, exists := s.wsConnections[connID]; exists {
				targets = append(targets, conn)
			}
		}
		return targets
	}

	// Otherwise, send to all subscribed connections
	for _, conn := range s.wsConnections {
		if s.shouldReceiveEvent(conn, event) {
			targets = append(targets, conn)
		}
	}

	return targets
}

// shouldReceiveEvent determines if a connection should receive an event
func (s *RealTimeSynchronizer) shouldReceiveEvent(conn *WebSocketConnection, event *SyncEvent) bool {
	// Check if connection is subscribed to this event type
	subscriptionKey := fmt.Sprintf("%s.%s", event.Type, event.Action)
	
	return contains(conn.Subscriptions, event.Type) || 
		   contains(conn.Subscriptions, subscriptionKey) ||
		   contains(conn.Subscriptions, "all")
}

// sendToConnection sends an event to a specific connection
func (s *RealTimeSynchronizer) sendToConnection(conn *WebSocketConnection, event *SyncEvent) {
	data, err := json.Marshal(event)
	if err != nil {
		s.logger.Errorf("Failed to marshal sync event: %v", err)
		return
	}

	select {
	case conn.SendQueue <- data:
		// Sent successfully
	default:
		s.logger.Warnf("⚠️ Connection %s send queue full, dropping event", conn.ID)
	}
}

// handleConnection handles a WebSocket connection
func (s *RealTimeSynchronizer) handleConnection(conn *WebSocketConnection) {
	defer s.RemoveConnection(conn.ID)

	// Start send handler
	go s.handleConnectionSend(conn)

	// Handle incoming messages
	for {
		var msg map[string]interface{}
		if err := conn.Conn.ReadJSON(&msg); err != nil {
			s.logger.Debugf("WebSocket read error for %s: %v", conn.ID, err)
			break
		}

		s.handleConnectionMessage(conn, msg)
	}
}

// handleConnectionSend handles sending messages to a connection
func (s *RealTimeSynchronizer) handleConnectionSend(conn *WebSocketConnection) {
	for data := range conn.SendQueue {
		if err := conn.Conn.WriteMessage(websocket.TextMessage, data); err != nil {
			s.logger.Debugf("WebSocket write error for %s: %v", conn.ID, err)
			break
		}
	}
}

// handleConnectionMessage handles incoming messages from a connection
func (s *RealTimeSynchronizer) handleConnectionMessage(conn *WebSocketConnection, msg map[string]interface{}) {
	msgType, _ := msg["type"].(string)

	switch msgType {
	case "ping":
		conn.LastPing = time.Now()
		s.sendToConnection(conn, &SyncEvent{
			Type:      "system",
			Action:    "pong",
			Timestamp: time.Now(),
		})
	case "subscribe":
		if subs, ok := msg["subscriptions"].([]interface{}); ok {
			subscriptions := make([]string, len(subs))
			for i, sub := range subs {
				subscriptions[i] = sub.(string)
			}
			s.Subscribe(conn.ID, subscriptions)
		}
	}
}

// startConnectionMonitor monitors connection health
func (s *RealTimeSynchronizer) startConnectionMonitor() {
	ticker := time.NewTicker(s.config.HeartbeatInterval)
	defer ticker.Stop()

	for range ticker.C {
		s.checkConnectionHealth()
	}
}

// checkConnectionHealth checks and cleans up unhealthy connections
func (s *RealTimeSynchronizer) checkConnectionHealth() {
	s.connectionsMutex.Lock()
	defer s.connectionsMutex.Unlock()

	cutoff := time.Now().Add(-2 * s.config.HeartbeatInterval)
	
	for connID, conn := range s.wsConnections {
		if conn.LastPing.Before(cutoff) {
			s.logger.Infof("🔌 Removing stale connection: %s", connID)
			conn.Conn.Close()
			delete(s.wsConnections, connID)
		}
	}
}

// startBatchProcessor processes events in batches for efficiency
func (s *RealTimeSynchronizer) startBatchProcessor() {
	// Implementation for batch processing
	s.logger.Info("📦 Batch processor started")
}

// Helper function
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetConnectionStats returns connection statistics
func (s *RealTimeSynchronizer) GetConnectionStats() map[string]interface{} {
	s.connectionsMutex.RLock()
	defer s.connectionsMutex.RUnlock()

	return map[string]interface{}{
		"total_connections": len(s.wsConnections),
		"queue_size":        len(s.syncQueue),
		"queue_capacity":    cap(s.syncQueue),
		"workers":           s.syncWorkers,
	}
}
