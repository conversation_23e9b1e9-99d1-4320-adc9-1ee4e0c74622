package performance

import (
	"context"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ⚡ ENERGY-EFFICIENT PERFORMANCE OPTIMIZER
// Sustainable, green computing patterns for Go applications

type Optimizer struct {
	logger          *zap.Logger
	config          *Config
	pools           map[string]*ResourcePool
	cache           *EfficientCache
	scheduler       *GreenScheduler
	circuitBreaker  *CircuitBreaker
	rateLimiter     *RateLimiter
	metrics         *PerformanceMetrics
	mu              sync.RWMutex
}

type Config struct {
	MaxGoroutines     int           `json:"max_goroutines"`
	PoolSize          int           `json:"pool_size"`
	CacheSize         int           `json:"cache_size"`
	CacheTTL          time.Duration `json:"cache_ttl"`
	GCTargetPercent   int           `json:"gc_target_percent"`
	MaxMemoryMB       int           `json:"max_memory_mb"`
	CPUThrottleLimit  float64       `json:"cpu_throttle_limit"`
	EnergyMode        EnergyMode    `json:"energy_mode"`
	BatchSize         int           `json:"batch_size"`
	IdleTimeout       time.Duration `json:"idle_timeout"`
	CompressionLevel  int           `json:"compression_level"`
}

type EnergyMode int

const (
	EnergyModePerformance EnergyMode = iota
	EnergyModeBalanced
	EnergyModeEcoFriendly
	EnergyModeUltraGreen
)

type ResourcePool struct {
	name      string
	resources chan interface{}
	factory   func() interface{}
	cleanup   func(interface{})
	maxSize   int
	created   int64
	borrowed  int64
	returned  int64
	mu        sync.RWMutex
}

type EfficientCache struct {
	data        map[string]*CacheEntry
	lru         *LRUList
	maxSize     int
	currentSize int
	ttl         time.Duration
	hits        int64
	misses      int64
	evictions   int64
	mu          sync.RWMutex
}

type CacheEntry struct {
	key       string
	value     interface{}
	size      int
	createdAt time.Time
	accessedAt time.Time
	frequency int
	compressed bool
	prev      *CacheEntry
	next      *CacheEntry
}

type LRUList struct {
	head *CacheEntry
	tail *CacheEntry
	size int
}

type GreenScheduler struct {
	tasks         chan *Task
	workers       []*Worker
	maxWorkers    int
	activeWorkers int
	idleTimeout   time.Duration
	cpuThreshold  float64
	mu            sync.RWMutex
}

type Task struct {
	ID          string
	Priority    Priority
	Function    func(context.Context) error
	Context     context.Context
	CreatedAt   time.Time
	StartedAt   *time.Time
	CompletedAt *time.Time
	Error       error
	Retries     int
	MaxRetries  int
}

type Priority int

const (
	PriorityLow Priority = iota
	PriorityNormal
	PriorityHigh
	PriorityCritical
)

type Worker struct {
	id          int
	tasks       chan *Task
	quit        chan bool
	active      bool
	lastActive  time.Time
	tasksRun    int64
	mu          sync.RWMutex
}

type CircuitBreaker struct {
	name           string
	maxFailures    int
	resetTimeout   time.Duration
	state          CircuitState
	failures       int
	lastFailureTime time.Time
	mu             sync.RWMutex
}

type CircuitState int

const (
	CircuitStateClosed CircuitState = iota
	CircuitStateOpen
	CircuitStateHalfOpen
)

type RateLimiter struct {
	tokens    chan struct{}
	rate      time.Duration
	burst     int
	lastToken time.Time
	mu        sync.RWMutex
}

type PerformanceMetrics struct {
	RequestCount      int64         `json:"request_count"`
	AverageLatency    time.Duration `json:"average_latency"`
	P95Latency        time.Duration `json:"p95_latency"`
	P99Latency        time.Duration `json:"p99_latency"`
	ErrorRate         float64       `json:"error_rate"`
	ThroughputRPS     float64       `json:"throughput_rps"`
	MemoryUsageMB     float64       `json:"memory_usage_mb"`
	CPUUsagePercent   float64       `json:"cpu_usage_percent"`
	GoroutineCount    int           `json:"goroutine_count"`
	GCPauseMs         float64       `json:"gc_pause_ms"`
	EnergyEfficiency  float64       `json:"energy_efficiency"`
	CacheHitRatio     float64       `json:"cache_hit_ratio"`
	PoolUtilization   float64       `json:"pool_utilization"`
	mu                sync.RWMutex
}

// NewOptimizer creates a new performance optimizer
func NewOptimizer(config *Config, logger *zap.Logger) *Optimizer {
	if config == nil {
		config = DefaultConfig()
	}

	optimizer := &Optimizer{
		logger:  logger,
		config:  config,
		pools:   make(map[string]*ResourcePool),
		metrics: &PerformanceMetrics{},
	}

	// Initialize components
	optimizer.cache = NewEfficientCache(config.CacheSize, config.CacheTTL)
	optimizer.scheduler = NewGreenScheduler(config.MaxGoroutines, config.IdleTimeout, config.CPUThrottleLimit)
	optimizer.circuitBreaker = NewCircuitBreaker("default", 5, 30*time.Second)
	optimizer.rateLimiter = NewRateLimiter(100, time.Second) // 100 requests per second

	// Apply energy mode optimizations
	optimizer.applyEnergyMode(config.EnergyMode)

	// Start background optimization
	go optimizer.startOptimization()

	logger.Info("Performance optimizer initialized",
		zap.String("energy_mode", config.EnergyMode.String()),
		zap.Int("max_goroutines", config.MaxGoroutines),
		zap.Int("cache_size", config.CacheSize),
	)

	return optimizer
}

func DefaultConfig() *Config {
	return &Config{
		MaxGoroutines:     runtime.NumCPU() * 2,
		PoolSize:          100,
		CacheSize:         1000,
		CacheTTL:          5 * time.Minute,
		GCTargetPercent:   100,
		MaxMemoryMB:       512,
		CPUThrottleLimit:  0.8,
		EnergyMode:        EnergyModeBalanced,
		BatchSize:         50,
		IdleTimeout:       30 * time.Second,
		CompressionLevel:  6,
	}
}

func (em EnergyMode) String() string {
	switch em {
	case EnergyModePerformance:
		return "performance"
	case EnergyModeBalanced:
		return "balanced"
	case EnergyModeEcoFriendly:
		return "eco_friendly"
	case EnergyModeUltraGreen:
		return "ultra_green"
	default:
		return "unknown"
	}
}

// applyEnergyMode applies energy-specific optimizations
func (o *Optimizer) applyEnergyMode(mode EnergyMode) {
	switch mode {
	case EnergyModePerformance:
		runtime.GOMAXPROCS(runtime.NumCPU())
		runtime.SetGCPercent(50) // More aggressive GC
		o.config.MaxGoroutines = runtime.NumCPU() * 4
		o.config.CacheSize = 2000
		
	case EnergyModeBalanced:
		runtime.GOMAXPROCS(runtime.NumCPU())
		runtime.SetGCPercent(100) // Default GC
		o.config.MaxGoroutines = runtime.NumCPU() * 2
		o.config.CacheSize = 1000
		
	case EnergyModeEcoFriendly:
		runtime.GOMAXPROCS(max(1, runtime.NumCPU()-1))
		runtime.SetGCPercent(200) // Less aggressive GC
		o.config.MaxGoroutines = runtime.NumCPU()
		o.config.CacheSize = 500
		
	case EnergyModeUltraGreen:
		runtime.GOMAXPROCS(max(1, runtime.NumCPU()/2))
		runtime.SetGCPercent(300) // Minimal GC
		o.config.MaxGoroutines = max(1, runtime.NumCPU()/2)
		o.config.CacheSize = 250
	}

	o.logger.Info("Energy mode applied",
		zap.String("mode", mode.String()),
		zap.Int("gomaxprocs", runtime.GOMAXPROCS(0)),
		zap.Int("max_goroutines", o.config.MaxGoroutines),
	)
}

// CreateResourcePool creates a new resource pool
func (o *Optimizer) CreateResourcePool(name string, factory func() interface{}, cleanup func(interface{})) *ResourcePool {
	pool := &ResourcePool{
		name:      name,
		resources: make(chan interface{}, o.config.PoolSize),
		factory:   factory,
		cleanup:   cleanup,
		maxSize:   o.config.PoolSize,
	}

	// Pre-populate pool based on energy mode
	prePopulate := o.config.PoolSize / 4
	if o.config.EnergyMode == EnergyModePerformance {
		prePopulate = o.config.PoolSize / 2
	} else if o.config.EnergyMode == EnergyModeUltraGreen {
		prePopulate = 1
	}

	for i := 0; i < prePopulate; i++ {
		pool.resources <- factory()
		pool.created++
	}

	o.mu.Lock()
	o.pools[name] = pool
	o.mu.Unlock()

	o.logger.Info("Resource pool created",
		zap.String("name", name),
		zap.Int("size", o.config.PoolSize),
		zap.Int("pre_populated", prePopulate),
	)

	return pool
}

// Get retrieves a resource from the pool
func (rp *ResourcePool) Get() interface{} {
	rp.mu.Lock()
	rp.borrowed++
	rp.mu.Unlock()

	select {
	case resource := <-rp.resources:
		return resource
	default:
		// Pool is empty, create new resource
		rp.mu.Lock()
		rp.created++
		rp.mu.Unlock()
		return rp.factory()
	}
}

// Put returns a resource to the pool
func (rp *ResourcePool) Put(resource interface{}) {
	rp.mu.Lock()
	rp.returned++
	rp.mu.Unlock()

	select {
	case rp.resources <- resource:
		// Successfully returned to pool
	default:
		// Pool is full, cleanup resource
		if rp.cleanup != nil {
			rp.cleanup(resource)
		}
	}
}

// NewEfficientCache creates a new efficient cache
func NewEfficientCache(maxSize int, ttl time.Duration) *EfficientCache {
	return &EfficientCache{
		data:    make(map[string]*CacheEntry),
		lru:     &LRUList{},
		maxSize: maxSize,
		ttl:     ttl,
	}
}

// Get retrieves a value from cache
func (c *EfficientCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	entry, exists := c.data[key]
	c.mu.RUnlock()

	if !exists {
		c.mu.Lock()
		c.misses++
		c.mu.Unlock()
		return nil, false
	}

	// Check TTL
	if time.Since(entry.createdAt) > c.ttl {
		c.mu.Lock()
		c.evict(entry)
		c.misses++
		c.mu.Unlock()
		return nil, false
	}

	// Update access time and frequency
	c.mu.Lock()
	entry.accessedAt = time.Now()
	entry.frequency++
	c.lru.moveToFront(entry)
	c.hits++
	c.mu.Unlock()

	return entry.value, true
}

// Set stores a value in cache
func (c *EfficientCache) Set(key string, value interface{}, size int) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Check if key already exists
	if existing, exists := c.data[key]; exists {
		existing.value = value
		existing.size = size
		existing.accessedAt = time.Now()
		existing.frequency++
		c.lru.moveToFront(existing)
		return
	}

	// Create new entry
	entry := &CacheEntry{
		key:        key,
		value:      value,
		size:       size,
		createdAt:  time.Now(),
		accessedAt: time.Now(),
		frequency:  1,
	}

	// Ensure we have space
	for c.currentSize+size > c.maxSize && c.lru.size > 0 {
		c.evictLRU()
	}

	// Add to cache
	c.data[key] = entry
	c.lru.addToFront(entry)
	c.currentSize += size
}

// evictLRU evicts the least recently used entry
func (c *EfficientCache) evictLRU() {
	if c.lru.tail != nil {
		c.evict(c.lru.tail)
	}
}

// evict removes an entry from cache
func (c *EfficientCache) evict(entry *CacheEntry) {
	delete(c.data, entry.key)
	c.lru.remove(entry)
	c.currentSize -= entry.size
	c.evictions++
}

// LRU list operations
func (lru *LRUList) addToFront(entry *CacheEntry) {
	if lru.head == nil {
		lru.head = entry
		lru.tail = entry
	} else {
		entry.next = lru.head
		lru.head.prev = entry
		lru.head = entry
	}
	lru.size++
}

func (lru *LRUList) remove(entry *CacheEntry) {
	if entry.prev != nil {
		entry.prev.next = entry.next
	} else {
		lru.head = entry.next
	}

	if entry.next != nil {
		entry.next.prev = entry.prev
	} else {
		lru.tail = entry.prev
	}

	entry.prev = nil
	entry.next = nil
	lru.size--
}

func (lru *LRUList) moveToFront(entry *CacheEntry) {
	lru.remove(entry)
	lru.addToFront(entry)
}

// NewGreenScheduler creates a new green scheduler
func NewGreenScheduler(maxWorkers int, idleTimeout time.Duration, cpuThreshold float64) *GreenScheduler {
	scheduler := &GreenScheduler{
		tasks:        make(chan *Task, maxWorkers*2),
		workers:      make([]*Worker, 0, maxWorkers),
		maxWorkers:   maxWorkers,
		idleTimeout:  idleTimeout,
		cpuThreshold: cpuThreshold,
	}

	// Start with minimal workers
	initialWorkers := max(1, maxWorkers/4)
	for i := 0; i < initialWorkers; i++ {
		scheduler.addWorker()
	}

	go scheduler.monitor()
	return scheduler
}

// Schedule schedules a task for execution
func (gs *GreenScheduler) Schedule(task *Task) {
	select {
	case gs.tasks <- task:
		// Task queued successfully
	default:
		// Queue is full, consider adding more workers
		gs.scaleUp()
		gs.tasks <- task
	}
}

// monitor monitors system resources and scales workers
func (gs *GreenScheduler) monitor() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		gs.adjustWorkers()
	}
}

// adjustWorkers adjusts the number of workers based on load
func (gs *GreenScheduler) adjustWorkers() {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	queueLength := len(gs.tasks)
	activeWorkers := gs.activeWorkers

	// Scale up if queue is building up
	if queueLength > activeWorkers*2 && activeWorkers < gs.maxWorkers {
		gs.addWorker()
	}

	// Scale down if workers are idle
	if queueLength == 0 && activeWorkers > 1 {
		gs.removeIdleWorker()
	}
}

func (gs *GreenScheduler) addWorker() {
	worker := &Worker{
		id:         len(gs.workers),
		tasks:      gs.tasks,
		quit:       make(chan bool),
		active:     true,
		lastActive: time.Now(),
	}

	gs.workers = append(gs.workers, worker)
	gs.activeWorkers++

	go worker.run()
}

func (gs *GreenScheduler) removeIdleWorker() {
	for i, worker := range gs.workers {
		if !worker.active && time.Since(worker.lastActive) > gs.idleTimeout {
			worker.quit <- true
			gs.workers = append(gs.workers[:i], gs.workers[i+1:]...)
			gs.activeWorkers--
			break
		}
	}
}

func (gs *GreenScheduler) scaleUp() {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.activeWorkers < gs.maxWorkers {
		gs.addWorker()
	}
}

// Worker run loop
func (w *Worker) run() {
	for {
		select {
		case task := <-w.tasks:
			w.executeTask(task)
		case <-w.quit:
			return
		case <-time.After(30 * time.Second):
			w.mu.Lock()
			w.active = false
			w.mu.Unlock()
		}
	}
}

func (w *Worker) executeTask(task *Task) {
	w.mu.Lock()
	w.active = true
	w.lastActive = time.Now()
	w.tasksRun++
	w.mu.Unlock()

	now := time.Now()
	task.StartedAt = &now

	err := task.Function(task.Context)
	
	completed := time.Now()
	task.CompletedAt = &completed
	task.Error = err
}

// startOptimization starts background optimization processes
func (o *Optimizer) startOptimization() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		o.optimizeMemory()
		o.updateMetrics()
	}
}

// optimizeMemory performs memory optimizations
func (o *Optimizer) optimizeMemory() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Force GC if memory usage is high
	memoryUsageMB := float64(m.Alloc) / 1024 / 1024
	if memoryUsageMB > float64(o.config.MaxMemoryMB)*0.8 {
		runtime.GC()
		o.logger.Info("Forced garbage collection",
			zap.Float64("memory_mb", memoryUsageMB),
			zap.Int("max_memory_mb", o.config.MaxMemoryMB),
		)
	}
}

// updateMetrics updates performance metrics
func (o *Optimizer) updateMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	o.metrics.mu.Lock()
	o.metrics.MemoryUsageMB = float64(m.Alloc) / 1024 / 1024
	o.metrics.GoroutineCount = runtime.NumGoroutine()
	o.metrics.GCPauseMs = float64(m.PauseNs[(m.NumGC+255)%256]) / 1000000

	// Calculate cache hit ratio
	if o.cache.hits+o.cache.misses > 0 {
		o.metrics.CacheHitRatio = float64(o.cache.hits) / float64(o.cache.hits+o.cache.misses)
	}

	o.metrics.mu.Unlock()
}

// Helper functions
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(name string, maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		name:         name,
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitStateClosed,
	}
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(burst int, rate time.Duration) *RateLimiter {
	rl := &RateLimiter{
		tokens:    make(chan struct{}, burst),
		rate:      rate,
		burst:     burst,
		lastToken: time.Now(),
	}

	// Fill initial tokens
	for i := 0; i < burst; i++ {
		rl.tokens <- struct{}{}
	}

	go rl.refillTokens()
	return rl
}

func (rl *RateLimiter) refillTokens() {
	ticker := time.NewTicker(rl.rate)
	defer ticker.Stop()

	for range ticker.C {
		select {
		case rl.tokens <- struct{}{}:
		default:
			// Bucket is full
		}
	}
}
